<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密查询结果</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: #e0e5ec;
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 导航菜单样式 */
        .nav-menu {
            display: flex;
            justify-content: center;
            flex-wrap: nowrap;
            gap: 12px;
            padding: 15px;
            margin: 20px auto 0;
            background-color: transparent;
            border-radius: 15px;
            box-shadow: none;
            max-width: 900px;
            width: 100%;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px 15px;
            border-radius: 50px;
            background-color: #e0e5ec;
            color: #444;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.5),
                        -5px -5px 10px rgba(255, 255, 255, 0.6);
            flex: 1;
            min-width: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            position: relative;
            z-index: 1;
        }
        
        .nav-item:hover {
            transform: translateY(-3px);
            box-shadow: 7px 7px 15px rgba(0, 0, 0, 0.15),
                        -7px -7px 15px rgba(255, 255, 255, 0.5),
                        inset 1px 1px 2px rgba(255, 255, 255, 0.2),
                        inset -1px -1px 2px rgba(0, 0, 0, 0.1);
            filter: brightness(1.05);
        }
        
        .nav-item:active {
            transform: translateY(0);
            box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.15),
                        -3px -3px 6px rgba(255, 255, 255, 0.5),
                        inset 2px 2px 4px rgba(0, 0, 0, 0.1),
                        inset -2px -2px 4px rgba(255, 255, 255, 0.05);
            filter: brightness(0.98);
        }
        
        .nav-item i {
            margin-right: 8px;
            font-size: 0.95em;
        }
        
        /* 响应式导航样式 */
        @media (max-width: 768px) {
            .nav-menu {
                flex-direction: row;
                flex-wrap: wrap;
                gap: 10px;
                padding: 10px;
            }
            
            .nav-item {
                padding: 8px 12px;
                font-size: 0.9rem;
                flex-basis: calc(50% - 10px);
                justify-content: center;
            }
        }
        
        @media (max-width: 480px) {
            .nav-menu {
                gap: 8px;
                padding: 8px;
            }
            
            .nav-item {
                padding: 8px 10px;
                font-size: 0.85rem;
                flex-basis: calc(50% - 8px);
            }
        }

        /* 容器样式 */
        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            width: 100%;
        }

        /* 标题样式 */
        header {
            text-align: center;
            padding: 40px 20px;
            background-color: #e0e5ec;
            box-shadow: 8px 8px 15px rgba(163, 177, 198, 0.5),
                        -8px -8px 15px rgba(255, 255, 255, 0.6);
            margin-bottom: 30px;
            border-radius: 15px;
        }

        header h1 {
            font-size: 2.5rem;
            color: #4CAF50;
            margin-bottom: 10px;
        }

        header p {
            font-size: 1.2rem;
            color: #666;
        }

        /* 结果容器样式 */
        .result-container {
            background-color: #e0e5ec;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 8px 8px 15px rgba(163, 177, 198, 0.5),
                        -8px -8px 15px rgba(255, 255, 255, 0.6);
        }

        .result-title {
            text-align: center;
            margin-bottom: 25px;
            color: #4CAF50;
            font-size: 1.6rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .result-title i {
            margin-right: 10px;
            color: #4CAF50;
        }

        /* 主要内容区域 */
        .main-content {
            margin-top: 20px;
        }

        /* 产品标题 */
        .product-title {
            text-align: center;
            font-size: 1.8rem;
            color: #1976D2;
            margin-bottom: 20px;
            font-weight: 500;
        }

        /* 设备限制行 */
        .device-limit {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .device-limit-label {
            display: flex;
            align-items: center;
            background-color: #e0e5ec;
            padding: 5px 15px;
            border-radius: 30px;
            color: #2e7d32;
            font-weight: 500;
            font-size: 0.9rem;
            margin-right: auto;
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.5),
                        inset -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        .device-limit-label i {
            margin-right: 5px;
        }

        .expiry-date {
            display: flex;
            align-items: center;
            color: #333;
        }

        .expiry-date-label {
            margin-right: 10px;
            font-weight: 500;
            color: #555;
        }

        .expiry-date-value {
            background-color: #ff9800;
            color: white;
            padding: 5px 15px;
            border-radius: 50px;
            font-weight: 500;
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5),
                        -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        /* 到期状态样式 */
        .expired-value {
            background-color: #f44336;
            color: white;
            padding: 5px 15px;
            border-radius: 50px;
            font-weight: 500;
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5),
                        -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        .expiry-alert {
            background-color: #e0e5ec;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 15px;
            display: flex;
            align-items: flex-start;
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.5),
                        inset -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        .expiry-alert i {
            color: #f44336;
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .expiry-alert.warning i {
            color: #ff9800;
        }

        .expiry-alert a {
            color: #1976D2;
            font-weight: 500;
            text-decoration: none;
        }

        .expiry-alert a:hover {
            text-decoration: underline;
        }

        /* 账号信息区域 */
        .account-info {
            margin-top: 30px;
            background-color: #e0e5ec;
            border-radius: 15px;
            padding: 20px;
            box-shadow: inset 5px 5px 10px rgba(163, 177, 198, 0.5),
                        inset -5px -5px 10px rgba(255, 255, 255, 0.6);
        }

        .account-info-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            color: #2196F3;
            font-size: 1.2rem;
            font-weight: 500;
        }

        .account-info-header i {
            margin-right: 10px;
            background-color: #2196F3;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5),
                        -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        .account-warning {
            background-color: #e0e5ec;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 15px;
            display: flex;
            align-items: flex-start;
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.5),
                        inset -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        .account-warning i {
            color: #ffc107;
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .account-field {
            display: flex;
            margin-bottom: 15px;
            align-items: center;
        }

        .account-label {
            background-color: #2196F3;
            color: white;
            padding: 8px 15px;
            border-radius: 50px 0 0 50px;
            width: 80px;
            text-align: center;
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5),
                        -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        .account-value {
            flex: 1;
            padding: 8px 15px;
            background-color: #e0e5ec;
            border-radius: 0 50px 50px 0;
            font-family: monospace;
            font-size: 1rem;
            position: relative;
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.5),
                        inset -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        .copy-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 3px 10px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s;
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5),
                        -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        .copy-btn:hover {
            background-color: #1976D2;
            transform: translateY(-50%) scale(1.05);
        }

        .copy-btn:active {
            transform: translateY(-50%) scale(0.95);
            box-shadow: 1px 1px 3px rgba(163, 177, 198, 0.5),
                        -1px -1px 3px rgba(255, 255, 255, 0.6);
        }

        .account-status {
            background-color: #e0e5ec;
            padding: 10px 15px;
            border-radius: 15px;
            margin-top: 10px;
            display: flex;
            align-items: center;
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.5),
                        inset -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        .account-status i {
            color: #4CAF50;
            margin-right: 10px;
        }

        /* 使用说明区域 */
        .usage-guide {
            margin-top: 30px;
            background-color: #e0e5ec;
            border-radius: 15px;
            padding: 20px;
            box-shadow: inset 5px 5px 10px rgba(163, 177, 198, 0.5),
                        inset -5px -5px 10px rgba(255, 255, 255, 0.6);
        }

        .usage-guide-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            color: #333;
            font-size: 1.2rem;
            font-weight: 500;
        }

        .usage-guide-header i {
            margin-right: 10px;
            background-color: #4CAF50;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5),
                        -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        .usage-steps {
            list-style-type: none;
            padding-left: 10px;
        }

        .usage-steps li {
            margin-bottom: 12px;
            position: relative;
            padding-left: 25px;
        }

        .usage-steps li:before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 0;
            width: 20px;
            height: 20px;
            background-color: #4CAF50;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            box-shadow: 2px 2px 4px rgba(163, 177, 198, 0.5),
                        -2px -2px 4px rgba(255, 255, 255, 0.6);
        }

        .usage-note {
            background-color: #e0e5ec;
            padding: 15px;
            margin-top: 15px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.5),
                        inset -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        .usage-note i {
            color: #2196F3;
            margin-right: 10px;
            font-size: 1.2rem;
        }

        /* 按钮样式 */
        .btn {
            display: block;
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 50px;
            background-color: #4CAF50;
            color: white;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.5),
                        -5px -5px 10px rgba(255, 255, 255, 0.6);
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
            margin-top: 20px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 7px 7px 15px rgba(163, 177, 198, 0.5),
                        -7px -7px 15px rgba(255, 255, 255, 0.6);
        }

        .btn:active {
            transform: translateY(0);
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5),
                        -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        .btn i {
            margin-right: 8px;
        }

        /* 页脚样式 */
        footer {
            text-align: center;
            padding: 20px;
            margin-top: auto;
            color: #666;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .result-container {
                padding: 20px;
            }

            header {
                padding: 30px 15px;
            }

            header h1 {
                font-size: 2rem;
            }

            .product-title {
                font-size: 1.5rem;
            }

            .device-limit {
                flex-direction: column;
                align-items: flex-start;
            }

            .device-limit-label {
                margin-bottom: 10px;
            }
        }

        /* 错误信息样式 */
        .error-container {
            text-align: center;
            padding: 30px;
        }

        .error-icon {
            font-size: 5rem;
            color: #FF5722;
            margin-bottom: 20px;
        }

        .error-message {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 20px;
        }

        /* 复制成功提示 */
        .copy-tooltip {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 12px 25px;
            border-radius: 50px;
            z-index: 1000;
            display: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            font-weight: 500;
            font-size: 1rem;
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        /* 提醒信息区域 */
        .reminder-info {
            margin-top: 20px;
            margin-bottom: 20px;
            background-color: #e0e5ec;
            border-radius: 15px;
            padding: 20px;
            box-shadow: inset 5px 5px 10px rgba(163, 177, 198, 0.5),
                        inset -5px -5px 10px rgba(255, 255, 255, 0.6);
        }

        .reminder-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            color: #856404;
            font-size: 1.2rem;
            font-weight: 500;
        }

        .reminder-header i {
            margin-right: 10px;
            color: #ffc107;
            font-size: 1.4rem;
            background-color: #e0e5ec;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5),
                        -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        .reminder-content {
            color: #856404;
            line-height: 1.6;
            background-color: #e0e5ec;
            padding: 15px;
            border-radius: 15px;
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.5),
                        inset -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        /* 验证码结果样式 - 优化后 */
        .verification-result-container {
            margin-top: 10px;
        }
        
        .verification-code-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 5px;
            flex-wrap: nowrap;
            background-color: #e0e5ec;
            padding: 15px;
            border-radius: 15px;
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.5),
                        inset -3px -3px 6px rgba(255, 255, 255, 0.6);
        }
        
        .verification-code-label {
            font-weight: 500;
            color: #212529;
            margin-right: 10px;
            white-space: nowrap;
        }
        
        .verification-code-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2196F3;
            letter-spacing: 3px;
            font-family: monospace;
            text-align: center;
            white-space: nowrap;
            background-color: #e0e5ec;
            padding: 5px 10px;
            border-radius: 10px;
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5),
                        -3px -3px 6px rgba(255, 255, 255, 0.6);
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
            margin: 0 10px;
        }
        
        .verification-copy-btn {
            white-space: nowrap;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 5px 15px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s;
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5),
                        -3px -3px 6px rgba(255, 255, 255, 0.6);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 70px;
        }
        
        .verification-copy-btn:hover {
            background-color: #1976D2;
            transform: scale(1.05);
        }
        
        .verification-copy-btn:active {
            transform: scale(0.95);
            box-shadow: 1px 1px 3px rgba(163, 177, 198, 0.5),
                        -1px -1px 3px rgba(255, 255, 255, 0.6);
        }
        
        .verification-copy-btn i {
            margin-right: 4px;
        }
        
        .verification-time {
            color: #6c757d;
            font-size: 0.8rem;
            white-space: nowrap;
            display: flex;
            align-items: center;
            padding: 3px 8px;
            border-radius: 15px;
            background-color: #e0e5ec;
            box-shadow: inset 2px 2px 5px rgba(163, 177, 198, 0.5),
                        inset -2px -2px 5px rgba(255, 255, 255, 0.6);
            min-width: 140px;
        }
        
        .verification-time i {
            margin-right: 4px;
            color: #2196F3;
        }
        
        .get-verification-btn {
            display: block;
            width: 100%;
            padding: 12px 15px;
            margin-top: 15px;
            border: none;
            border-radius: 50px;
            background-color: #2196F3;
            color: white;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.5),
                      -5px -5px 10px rgba(255, 255, 255, 0.6);
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .get-verification-btn:hover {
            background-color: #1976D2;
            transform: translateY(-3px);
            box-shadow: 7px 7px 15px rgba(163, 177, 198, 0.5),
                      -7px -7px 15px rgba(255, 255, 255, 0.6);
        }
        
        .get-verification-btn:active {
            transform: translateY(0);
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5),
                      -3px -3px 6px rgba(255, 255, 255, 0.6);
        }
        
        .get-verification-btn i {
            margin-right: 8px;
        }
        
        #verificationResult {
            margin-top: 15px;
            background-color: #e0e5ec;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.5),
                      inset -3px -3px 6px rgba(255, 255, 255, 0.6);
        }
        
        .verification-alert {
            background-color: #e0e5ec;
            padding: 15px;
            margin-bottom: 0;
            border-radius: 15px;
            display: flex;
            align-items: center;
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.5),
                      inset -3px -3px 6px rgba(255, 255, 255, 0.6);
        }
        
        .verification-alert.success {
            color: #155724;
            background-color: #e0e5ec;
        }
        
        .verification-alert.success .verification-code-row {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .verification-alert.warning {
            color: #856404;
            background-color: #e0e5ec;
        }
        
        .verification-alert.error {
            color: #721c24;
            background-color: #e0e5ec;
        }
        
        .verification-alert i {
            margin-right: 10px;
            font-size: 1.2rem;
        }
        
        .loading-spinner {
            text-align: center;
            padding: 20px;
        }
        
        .loading-spinner i {
            font-size: 2rem;
            color: #2196F3;
        }
        
        .loading-spinner p {
            margin-top: 10px;
            color: #555;
        }
        
        @media (max-width: 576px) {
            .verification-code-row {
                flex-wrap: wrap;
                justify-content: center;
                padding: 12px;
            }
            
            .verification-code-value {
                margin: 8px 0;
                font-size: 1.6rem;
                text-align: center;
                width: 100%;
                order: -1; /* 验证码显示在最上面 */
            }
            
            .verification-time {
                margin: 5px 0;
                width: 100%;
                text-align: center;
                justify-content: center;
            }
            
            .verification-copy-btn {
                margin: 5px 0;
                padding: 8px 15px;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航菜单 -->
        <div class="nav-menu" id="nav-menu">
            <!-- 导航项将通过JavaScript动态加载 -->
        </div>

        <div id="loading" style="text-align: center; padding: 40px;">
            <i class="fas fa-spinner fa-spin" style="font-size: 3rem; color: #4CAF50;"></i>
            <p style="margin-top: 20px; font-size: 1.2rem;">正在加载卡密信息...</p>
        </div>

        <div id="result-success" class="result-container" style="display: none;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <div class="result-title" style="margin-bottom: 0;">
                    <i class="fas fa-check-circle"></i>
                    <span>验证成功</span>
                </div>
                
                <a href="index.html" class="btn" style="margin-top: 0; width: auto; padding: 8px 15px;">
                    <i class="fas fa-home"></i>
                    <span>返回首页</span>
                </a>
            </div>
            
            <div class="main-content">
                <div class="product-title" id="product-title">Adobe-全家桶授权-14天</div>
                
                <div class="device-limit">
                    <div class="expiry-date" style="margin-right: auto;">
                        <span class="expiry-date-label">可使用设备数量：</span>
                        <span class="expiry-date-value" id="device-limit-text" style="background-color: #4CAF50;">仅限1台电脑</span>
                    </div>
                    
                    <div class="expiry-date">
                        <span class="expiry-date-label">到期时间：</span>
                        <span class="expiry-date-value" id="expiry-date-value">2025年06月19日</span>
                    </div>
                </div>
                
                <div style="text-align: right; margin-top: 5px; margin-bottom: 15px;">
                    <span style="font-size: 0.85rem; color: #666; display: flex; align-items: center; justify-content: flex-end;">
                        <i class="fas fa-info-circle" style="margin-right: 5px;"></i>
                        以具体购买时间为准哦~到期时间可能会出现误差
                    </span>
                </div>
                
                <!-- 到期提醒区域 -->
                <div class="expiry-alert warning" id="expiry-warning" style="display: none; background-color: #2196F3; box-shadow: inset 3px 3px 6px rgba(33, 150, 243, 0.5), inset -3px -3px 6px rgba(255, 255, 255, 0.3);">
                    <div style="text-align: center; width: 100%; color: #fff;">
                        账号即将到期，如需续费当前账号请提前联系人工客服哦！<br>
                    </div>
                </div>
                
                <!-- 已到期提醒区域 -->
                <div class="expiry-alert" id="expiry-expired" style="display: none; background-color: #f44336; box-shadow: inset 3px 3px 6px rgba(244, 67, 54, 0.5), inset -3px -3px 6px rgba(255, 255, 255, 0.3);">
                    <div style="text-align: center; width: 100%; color: #fff;">
                        账号已到期！请重新购买哦~（若续费账号请提前1-2天联系人工客服）<br>
                    </div>
                </div>
                
                <div class="account-info">
                    <div class="account-info-header">
                        <i class="fas fa-user"></i>
                        <span>账号信息</span>
                    </div>
                    
                    <div class="account-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <div>以下是您的Adobe账号信息，请妥善保管：</div>
                    </div>
                    
                    <div class="account-field">
                        <div class="account-label">账号</div>
                        <div class="account-value" id="account-value">
                            <span id="account-text"></span>
                            <button class="copy-btn" id="account-copy-btn" onclick="copyText('account-text')">复制</button>
                        </div>
                    </div>
                    
                    <div class="account-field">
                        <div class="account-label">密码</div>
                        <div class="account-value" id="password-value">
                            <span id="password-text"></span>
                            <button class="copy-btn" id="password-copy-btn" onclick="copyText('password-text')">复制</button>
                        </div>
                    </div>
                    
                    <!-- 账号状态信息 -->
                    <div class="account-status" id="account-status" style="display: none;">
                        <i class="fas fa-check-circle"></i>
                        <span id="account-status-text">新账号已更新，注销旧账号登录新账号！</span>
                    </div>
                    
                    <!-- 新账号信息区域，默认隐藏 -->
                    <div id="new-account-container" style="display: none; margin-top: 20px;">
                        <div class="account-field">
                            <div class="account-label" style="background-color: #FF9800;">新账号</div>
                            <div class="account-value" id="new-account-value">
                                <span id="new-account-text"></span>
                                <button class="copy-btn" style="background-color: #FF9800;" onclick="copyText('new-account-text')">复制</button>
                            </div>
                        </div>
                        
                        <div class="account-field">
                            <div class="account-label" style="background-color: #FF9800;">新密码</div>
                            <div class="account-value" id="new-password-value">
                                <span id="new-password-text"></span>
                                <button class="copy-btn" style="background-color: #FF9800;" onclick="copyText('new-password-text')">复制</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 统一的验证码获取按钮区域 -->
                <div id="verification-container" style="margin-top: 15px;">
                    <button id="getVerificationBtn" class="get-verification-btn">
                        <i class="fas fa-key"></i>获取验证码
                    </button>
                    <div id="verification-account-info" style="margin-top: 5px; font-size: 0.85rem; color: #666; text-align: center;"></div>
                    <div id="verificationResult"></div>
                </div>
                
                <!-- 提醒信息区域，只在有内容时显示 -->
                <div class="reminder-info" id="reminder-container" style="display: none;">
                    <div class="reminder-header">
                        <i class="fas fa-exclamation-circle"></i>
                        <span>提醒</span>
                    </div>
                    <div class="reminder-content" id="reminder-content"></div>
                </div>
                
                <div class="usage-guide">
                    <div class="usage-guide-header">
                        <i class="fas fa-book"></i>
                        <span>使用说明</span>
                    </div>
                    
                    <ol class="usage-steps" style="counter-reset: step-counter;">
                        <li>第1次使用安装教程：<a href="https://www.kdocs.cn/l/cgAVylA5CUby" target="_blank" rel="noopener noreferrer">点击查看安装教程</a></li>
                        <li>老用户切换账号：<a href="https://www.kdocs.cn/l/cr4WYtZlaoCM" target="_blank" rel="noopener noreferrer">点击查看切换账号教程</a></li>
                        <li>安装报错问题：若遇到安装报错问题，可联系客服付费远程安装。</li>
                        <li>联系客服：如遇到问题，请重新验证卡密或联系客服</li>
                    </ol>
                </div>
            </div>
        </div>

        <div id="result-error" class="result-container error-container" style="display: none;">
            <div class="error-icon">
                <i class="fas fa-times-circle"></i>
            </div>
            <div class="error-message" id="error-message">未找到卡密信息</div>
            <a href="index.html" class="btn">
                <i class="fas fa-search"></i>
                <span>返回查询</span>
            </a>
        </div>
    </div>

    <footer>
        <p>&copy; 2023 卡密查询系统. 保留所有权利.</p>
    </footer>

    <div class="copy-tooltip" id="copy-tooltip">复制成功！</div>

    <script>
    // 加载链接配置
    fetch('api/get_links.php')
        .then(response => response.json())
        .then(data => {
            if(data.code == 200) {
                // 清空导航菜单
                const navMenu = document.getElementById('nav-menu');
                navMenu.innerHTML = '';
                
                // 动态添加导航项
                if (data.links && data.links.length > 0) {
                    data.links.forEach(link => {
                        if (link.url) {
                            const navItem = document.createElement('a');
                            navItem.href = typeof link === 'object' ? link.url : link;
                            navItem.className = 'nav-item';
                            navItem.target = '_blank'; // 在新标签页中打开
                            navItem.rel = 'noopener noreferrer'; // 添加安全属性，防止链接被拦截
                            
                            // 如果有自定义颜色，应用它
                            if (link.color) {
                                navItem.style.backgroundColor = link.color;
                                navItem.style.color = '#ffffff'; // 使用白色文本以提高可读性
                                navItem.style.boxShadow = `5px 5px 10px rgba(0, 0, 0, 0.2), 
                                              -5px -5px 10px rgba(255, 255, 255, 0.1),
                                              inset 1px 1px 2px rgba(255, 255, 255, 0.2),
                                              inset -1px -1px 2px rgba(0, 0, 0, 0.1)`;
                            }
                            
                            // 添加图标
                            const icon = document.createElement('i');
                            icon.className = 'fas ' + (link.icon || 'fa-link');
                            navItem.appendChild(icon);
                            
                            // 添加文本
                            const text = document.createTextNode(link.display_name || link.name);
                            navItem.appendChild(text);
                            
                            // 添加到菜单
                            navMenu.appendChild(navItem);
                        }
                    });
                } else {
                    // 兼容旧版API返回格式
                    if (data.links.buy_account) {
                        addNavItem(navMenu, data.links.buy_account, 'fa-shopping-cart', '购买账号', data.links.buy_account.color);
                    }
                    if (data.links.faq) {
                        addNavItem(navMenu, data.links.faq, 'fa-question-circle', '问题解答', data.links.faq.color);
                    }
                    if (data.links.customer_service) {
                        addNavItem(navMenu, data.links.customer_service, 'fa-headset', '咨询客服', data.links.customer_service.color);
                    }
                    if (data.links.extra_link_1 && data.links.extra_link_1.is_active) {
                        addNavItem(navMenu, data.links.extra_link_1, data.links.extra_link_1.icon || 'fa-link', 
                                  data.links.extra_link_1.display_name || '额外链接1', data.links.extra_link_1.color);
                    }
                    if (data.links.extra_link_2 && data.links.extra_link_2.is_active) {
                        addNavItem(navMenu, data.links.extra_link_2, data.links.extra_link_2.icon || 'fa-link', 
                                  data.links.extra_link_2.display_name || '额外链接2', data.links.extra_link_2.color);
                    }
                }
                
                // 根据导航项数量调整样式
                adjustNavItemStyles();
            } else {
                console.error('加载链接配置失败:', data.error);
            }
        })
        .catch(error => {
            console.error('请求链接配置失败:', error);
        });
        
    // 辅助函数：添加导航项
    function addNavItem(navMenu, link, iconClass, text, color) {
        const navItem = document.createElement('a');
        navItem.href = typeof link === 'object' ? link.url : link;
        navItem.className = 'nav-item';
        navItem.target = '_blank'; // 在新标签页中打开
        navItem.rel = 'noopener noreferrer'; // 添加安全属性，防止链接被拦截
        
        // 如果有自定义颜色，应用它
        if (color) {
            navItem.style.backgroundColor = color;
            navItem.style.color = '#ffffff'; // 使用白色文本以提高可读性
            navItem.style.boxShadow = `5px 5px 10px rgba(0, 0, 0, 0.2), 
                          -5px -5px 10px rgba(255, 255, 255, 0.1),
                          inset 1px 1px 2px rgba(255, 255, 255, 0.2),
                          inset -1px -1px 2px rgba(0, 0, 0, 0.1)`;
        }
        
        // 添加图标
        const icon = document.createElement('i');
        icon.className = 'fas ' + iconClass;
        navItem.appendChild(icon);
        
        // 添加文本
        navItem.appendChild(document.createTextNode(text));
        
        // 添加到菜单
        navMenu.appendChild(navItem);
    }
    
    // 根据导航项数量调整样式
    function adjustNavItemStyles() {
        const navMenu = document.getElementById('nav-menu');
        const navItems = navMenu.querySelectorAll('.nav-item');
        const itemCount = navItems.length;
        
        if (itemCount === 0) {
            navMenu.style.display = 'none';
            return;
        }
        
        navMenu.style.display = 'flex';
        
        // 根据数量调整导航项样式
        if (itemCount <= 3) {
            // 3个或更少的项目，字体和padding略大一些
            navItems.forEach(item => {
                item.style.fontSize = '0.95rem';
                item.style.padding = '10px 15px';
            });
        } else if (itemCount === 4) {
            // 4个项目，稍微缩小
            navItems.forEach(item => {
                item.style.fontSize = '0.9rem';
                item.style.padding = '8px 12px';
            });
        } else if (itemCount >= 5) {
            // 5个或更多项目，更加紧凑
            navItems.forEach(item => {
                item.style.fontSize = '0.85rem';
                item.style.padding = '8px 10px';
                
                // 简化图标和文本之间的间距
                const icon = item.querySelector('i');
                if (icon) {
                    icon.style.marginRight = '4px';
                }
            });
        }
        
        // 确保导航菜单宽度不超过容器
        const containerWidth = document.querySelector('.container').offsetWidth;
        navMenu.style.maxWidth = (containerWidth - 40) + 'px'; // 减去一些边距
    }
        
    document.addEventListener('DOMContentLoaded', function() {
        // 获取URL中的卡密参数
        const path = window.location.pathname;
        let kami = path.substring(path.lastIndexOf('/') + 1);
        
        // 如果没有卡密参数，显示错误
        if (!kami) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('result-error').style.display = 'block';
            document.getElementById('error-message').textContent = '未提供卡密信息';
            return;
        }
        
        // 解码URL中的特殊字符（如果需要）
        try {
            // 检查是否已经是URL编码格式，如果是则解码
            if (kami.includes('%')) {
                kami = decodeURIComponent(kami);
            }
        } catch (e) {
            console.error('解码卡密失败:', e);
        }
        
        // 添加时间戳参数以避免缓存
        const timestamp = new Date().getTime();
        
        // 发起请求查询卡密信息
        fetch(`api/kmcx.php?kami=${encodeURIComponent(kami)}&_t=${timestamp}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('请求失败');
                }
                return response.json();
            })
            .then(data => {
                // 隐藏加载提示
                document.getElementById('loading').style.display = 'none';
                
                if (data.code == 200) {
                    // 显示成功结果
                    document.getElementById('result-success').style.display = 'block';
                    
                    // 设置产品标题
                    document.getElementById('product-title').textContent = data.type;
                    
                    // 设置设备限制
                    document.getElementById('device-limit-text').textContent = data.device_limit;
                    
                    // 处理到期时间显示
                    if (data.expiry_time) {
                        const today = new Date();
                        const expDate = new Date(data.expiry_time);
                        
                        // 计算卡密是否已到期
                        const isExpired = today > expDate;
                        
                        // 计算卡密是否在30天内已到期
                        const thirtyDaysAgo = new Date();
                        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                        const isWithin30DaysExpired = expDate > thirtyDaysAgo;
                        
                        // 计算卡密是否在7天内即将到期
                        const sevenDaysLater = new Date();
                        sevenDaysLater.setDate(sevenDaysLater.getDate() + 7);
                        const isAboutToExpire = !isExpired && expDate <= sevenDaysLater;
                        
                        // 处理显示逻辑
                        if (isExpired) {
                            // 如果已到期但在30天内，仍然可以查询
                            if (isWithin30DaysExpired) {
                                document.getElementById('expiry-date-value').className = 'expired-value';
                                document.getElementById('expiry-date-value').textContent = '已到期';
                                document.getElementById('expiry-expired').style.display = 'flex';
                            } else {
                                // 超过30天的已到期卡密
                                document.getElementById('loading').style.display = 'none';
                                document.getElementById('result-success').style.display = 'none';
                                document.getElementById('result-error').style.display = 'block';
                                document.getElementById('error-message').textContent = '卡密已过期超过30天，无法查询';
                                return;
                            }
                        } else {
                            // 未到期，显示正常日期（只显示年月日，但实际计算精确到时分秒）
                            const formattedDate = `${expDate.getFullYear()}年${String(expDate.getMonth() + 1).padStart(2, '0')}月${String(expDate.getDate()).padStart(2, '0')}日`;
                            document.getElementById('expiry-date-value').textContent = formattedDate;
                            
                            // 如果即将到期，显示提醒
                            if (isAboutToExpire) {
                                document.getElementById('expiry-warning').style.display = 'flex';
                            }
                        }
                    } else if (data.valid_days) {
                        // 如果没有到期时间但有有效天数，则显示有效天数
                        document.getElementById('expiry-date-value').textContent = `${data.valid_days}天`;
                    }
                    
                    // 设置账号和密码
                    document.getElementById('account-text').textContent = kami;
                    
                    // 如果有密码信息，显示密码
                    if (data.password && data.password !== '无') {
                        document.getElementById('password-text').textContent = data.password;
                    } else {
                        document.getElementById('password-text').textContent = '无需密码';
                    }
                    
                    // 处理新账号信息（如果存在）
                    if (data.account_changed && data.new_account) {
                        // 显示新账号区域
                        document.getElementById('new-account-container').style.display = 'block';
                        // 显示账号状态信息
                        document.getElementById('account-status').style.display = 'flex';
                        
                        // 设置新账号和密码
                        document.getElementById('new-account-text').textContent = data.new_account;
                        
                        if (data.new_password && data.new_password !== '无') {
                            document.getElementById('new-password-text').textContent = data.new_password;
                        } else {
                            document.getElementById('new-password-text').textContent = '无需密码';
                        }
                        
                        // 禁用旧账号复制功能
                        disableOldAccount();
                        
                        // 更新验证码提示信息
                        updateVerificationAccountInfo();
                    }
                    
                    // 如果有备注信息，显示在提醒区域
                    if (data.remark) {
                        document.getElementById('reminder-container').style.display = 'block';
                        document.getElementById('reminder-content').textContent = data.remark;
                    }
                    
                    // 设置页面标题
                    document.title = `卡密查询结果 - ${kami}`;
                } else {
                    // 显示错误信息
                    document.getElementById('result-error').style.display = 'block';
                    document.getElementById('error-message').textContent = data.msg || '未找到卡密信息';
                }
            })
            .catch(error => {
                // 隐藏加载提示
                document.getElementById('loading').style.display = 'none';
                
                // 显示错误信息
                document.getElementById('result-error').style.display = 'block';
                document.getElementById('error-message').textContent = error.message || '查询失败，请稍后再试';
                console.error('查询卡密失败:', error);
            });
            
        // 为复制按钮添加事件监听
        document.querySelectorAll('.copy-btn').forEach(function(button) {
            button.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();
                const targetId = this.getAttribute('onclick').match(/'([^']+)'/)[1];
                copyText(targetId);
            });
        });
        
        // 添加获取验证码按钮的事件监听
        setupVerificationButtons();
    });
    
    // 禁用旧账号的复制功能，并设置为灰色
    function disableOldAccount() {
        // 获取旧账号和密码的元素
        const accountValue = document.getElementById('account-value');
        const passwordValue = document.getElementById('password-value');
        const accountCopyBtn = document.getElementById('account-copy-btn');
        const passwordCopyBtn = document.getElementById('password-copy-btn');
        
        // 添加灰色样式
        accountValue.style.opacity = '0.6';
        passwordValue.style.opacity = '0.6';
        
        // 禁用复制按钮
        accountCopyBtn.disabled = true;
        passwordCopyBtn.disabled = true;
        accountCopyBtn.style.backgroundColor = '#9e9e9e';
        passwordCopyBtn.style.backgroundColor = '#9e9e9e';
        accountCopyBtn.style.cursor = 'not-allowed';
        passwordCopyBtn.style.cursor = 'not-allowed';
        
        // 移除点击事件
        accountCopyBtn.onclick = function(e) {
            e.preventDefault();
            return false;
        };
        passwordCopyBtn.onclick = function(e) {
            e.preventDefault();
            return false;
        };
    }
    
    // 增强版复制文本功能，兼容更多浏览器
    function copyText(elementId) {
        const text = document.getElementById(elementId).textContent;
        
        // 尝试使用现代 Clipboard API
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(text)
                .then(showCopySuccess)
                .catch(function(err) {
                    console.warn('Clipboard API 失败，尝试回退方法', err);
                    fallbackCopyTextToClipboard(text);
                });
        } else {
            // 回退到传统方法
            fallbackCopyTextToClipboard(text);
        }
    }
    
    // 传统复制方法
    function fallbackCopyTextToClipboard(text) {
        // 创建临时文本区域
        const textArea = document.createElement('textarea');
        textArea.value = text;
        
        // 设置样式使其不可见
        textArea.style.position = 'fixed';
        textArea.style.top = '0';
        textArea.style.left = '0';
        textArea.style.width = '2em';
        textArea.style.height = '2em';
        textArea.style.padding = '0';
        textArea.style.border = 'none';
        textArea.style.outline = 'none';
        textArea.style.boxShadow = 'none';
        textArea.style.background = 'transparent';
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showCopySuccess();
            } else {
                console.error('复制失败');
            }
        } catch (err) {
            console.error('复制过程出错:', err);
        }
        
        document.body.removeChild(textArea);
    }
    
    // 显示复制成功提示
    function showCopySuccess() {
        const tooltip = document.getElementById('copy-tooltip');
        
        // 显示提示
        tooltip.style.display = 'block';
        
        // 添加动画效果
        tooltip.style.opacity = '0';
        tooltip.style.transform = 'translate(-50%, -40%)';
        
        // 强制重绘
        void tooltip.offsetWidth;
        
        // 应用动画
        tooltip.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        tooltip.style.opacity = '1';
        tooltip.style.transform = 'translate(-50%, -50%)';
        
        // 2秒后隐藏提示
        setTimeout(function() {
            tooltip.style.opacity = '0';
            tooltip.style.transform = 'translate(-50%, -60%)';
            
            // 完全隐藏
            setTimeout(function() {
                tooltip.style.display = 'none';
                tooltip.style.transform = 'translate(-50%, -50%)';
            }, 300);
        }, 2000);
    }
    
    // 设置验证码按钮
    function setupVerificationButtons() {
        // 获取验证码按钮
        const getVerificationBtn = document.getElementById('getVerificationBtn');
        
        // 初始化提示信息
        updateVerificationAccountInfo();
        
        // 设置验证码按钮事件
        if (getVerificationBtn) {
            getVerificationBtn.addEventListener('click', function() {
                // 检查是否有新账号
                const newAccountContainer = document.getElementById('new-account-container');
                let emailToCheck;
                
                if (newAccountContainer && newAccountContainer.style.display !== 'none') {
                    // 有新账号，使用新账号
                    emailToCheck = document.getElementById('new-account-text').textContent.trim();
                } else {
                    // 没有新账号，使用原始账号
                    emailToCheck = document.getElementById('account-text').textContent.trim();
                }
                
                getVerificationCode(emailToCheck, 'verificationResult');
            });
        }
    }
    
    // 更新验证码账号提示信息
    function updateVerificationAccountInfo() {
        const newAccountContainer = document.getElementById('new-account-container');
        const infoElement = document.getElementById('verification-account-info');
        
        if (!infoElement) return;
        
        if (newAccountContainer && newAccountContainer.style.display !== 'none') {
            // 有新账号，提示使用新账号
            const newAccount = document.getElementById('new-account-text').textContent.trim();
            infoElement.innerHTML = `<i class="fas fa-info-circle"></i> 将获取新账号 <span style="color: #FF9800; font-weight: bold;">${newAccount}</span> 的验证码`;
        } else {
            // 没有新账号，提示使用原始账号
            const account = document.getElementById('account-text').textContent.trim();
            infoElement.innerHTML = `<i class="fas fa-info-circle"></i> 将获取账号 <span style="color: #2196F3; font-weight: bold;">${account}</span> 的验证码`;
        }
    }
    
    // 获取验证码函数
    function getVerificationCode(email, resultContainerId) {
        if (!email) {
            document.getElementById(resultContainerId).innerHTML = 
                '<div class="verification-alert error"><i class="fas fa-exclamation-circle"></i>未找到有效的账号信息</div>';
            return;
        }
        
        // 显示加载状态
        const resultContainer = document.getElementById(resultContainerId);
        resultContainer.innerHTML = 
            '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i><p>正在获取验证码...</p></div>';
        
        // 创建一个超时计时器
        const loadingTimeout = setTimeout(() => {
            resultContainer.innerHTML = 
                '<div class="verification-alert warning"><i class="fas fa-exclamation-triangle"></i>查询时间较长，请稍后再试</div>';
        }, 8000); // 8秒后显示超时提示
        
        // 发起API请求获取验证码
        fetch(`api/getVerification.php?email=${encodeURIComponent(email)}&_t=${new Date().getTime()}`)
            .then(response => {
                clearTimeout(loadingTimeout);
                if (!response.ok) {
                    throw new Error('验证码查询失败');
                }
                return response.json();
            })
            .then(data => {
                if (!data.results || data.results.length === 0) {
                    resultContainer.innerHTML = 
                        '<div class="verification-alert warning"><i class="fas fa-exclamation-triangle"></i>未找到相关邮件，请确认是否已经点击获取验证码按钮</div>';
                    return;
                }
                
                // 过滤出属于该邮箱的邮件
                const filteredMails = data.results.filter(mail => 
                    mail.address && mail.address.toLowerCase() === email.toLowerCase()
                );
                
                if (filteredMails.length === 0) {
                    resultContainer.innerHTML = 
                        '<div class="verification-alert warning"><i class="fas fa-exclamation-triangle"></i>未找到相关邮件，请确认是否已经点击获取验证码按钮</div>';
                    return;
                }
                
                // 提取验证码和时间
                try {
                    const matchedEmail = filteredMails[0]; // 获取最新的一封邮件
                    const emailContent = matchedEmail.raw || '';
                    const verificationCode = extractVerificationCode(emailContent);
                    const emailTime = formatTime(matchedEmail.created_at);
                    
                    if (verificationCode) {
                        const resultHtml = `
                            <div class="verification-code-row">
                                <div class="verification-time">
                                    <i class="fas fa-clock"></i> ${emailTime}
                                </div>
                                <div class="verification-code-value">${verificationCode}</div>
                                <button class="verification-copy-btn" onclick="copyVerificationCode('${verificationCode}')">
                                    <i class="fas fa-copy"></i> 复制
                                </button>
                            </div>
                        `;
                        
                        resultContainer.innerHTML = resultHtml;
                    } else {
                        resultContainer.innerHTML = 
                            '<div class="verification-alert warning"><i class="fas fa-exclamation-triangle"></i>未能从邮件中提取到验证码，可能还未收到验证邮件</div>';
                    }
                } catch (e) {
                    resultContainer.innerHTML = 
                        `<div class="verification-alert error"><i class="fas fa-exclamation-circle"></i>解析邮件内容出错: ${e.message}</div>`;
                }
            })
            .catch(error => {
                clearTimeout(loadingTimeout);
                resultContainer.innerHTML = 
                    `<div class="verification-alert error"><i class="fas fa-exclamation-circle"></i>查询验证码失败: ${error.message || '网络错误'}</div>`;
            });
    }
    
    // 复制验证码
    function copyVerificationCode(code) {
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(code)
                .then(showCopySuccess)
                .catch(function(err) {
                    fallbackCopyTextToClipboard(code);
                });
        } else {
            fallbackCopyTextToClipboard(code);
        }
    }
    
    // 提取验证码功能
    function extractVerificationCode(rawEmail) {
        // 尝试匹配验证码格式
        const verificationPatterns = [
            /(?:验证码|verification code|code)[^0-9]*([0-9]{6})(?!\d)/i,
            /您的验证码(?:是|为)?[^0-9]*([0-9]{6})(?!\d)/,
            /[^0-9]([0-9]{6})[^0-9]*(?:是|为)(?:您的)?验证码/,
            /动态验证码[^0-9]*([0-9]{6})(?!\d)/,
            /code is[^0-9]*([0-9]{6})(?!\d)/i,
            /verification code is[^0-9]*([0-9]{6})(?!\d)/i,
            /\D([0-9]{6})(?!\d)/
        ];
        
        for (const pattern of verificationPatterns) {
            const match = rawEmail.match(pattern);
            if (match && match[1]) {
                return match[1];
            }
        }
        
        // 如果以上模式都没匹配到，尝试查找6位数字
        const numberMatches = rawEmail.match(/\b\d{6}\b/g);
        if (numberMatches && numberMatches.length > 0) {
            return numberMatches[0];
        }
        
        return null;
    }
    
    // 格式化时间
    function formatTime(dateString) {
        if (!dateString) return '';
        
        try {
            // 创建一个日期对象
            const date = new Date(dateString);
            
            // 检查日期是否有效
            if (isNaN(date.getTime())) {
                return dateString;
            }
            
            // 中国时区修正（+8小时）
            const chinaDate = new Date(date.getTime() + 8 * 60 * 60 * 1000);
            
            // 获取月份和日期
            const month = (chinaDate.getMonth() + 1).toString().padStart(2, '0');
            const day = chinaDate.getDate().toString().padStart(2, '0');
            
            // 获取时间（时分秒）- 使用24小时制
            const hours = chinaDate.getHours().toString().padStart(2, '0');
            const minutes = chinaDate.getMinutes().toString().padStart(2, '0');
            const seconds = chinaDate.getSeconds().toString().padStart(2, '0');
            
            // 返回格式化的时间
            return `${month}/${day} ${hours}:${minutes}:${seconds}`;
        } catch (e) {
            console.error('日期格式化错误:', e);
            return dateString;
        }
    }
</script>
</body>
</html> 