<?php

// 数据库配置
include 'config.php';
// 检查是否是 GET 请求
if ($_SERVER["REQUEST_METHOD"] == "GET") {
    // 获取 GET 数据
    $jqr = $_GET['jqr'] ?? '';
    $lxr = $_GET['lxr'] ?? '';
    $type = $_GET['type'] ?? ''; // 获取授权类型
    $out_trade_no = $_GET['out_trade_no'] ?? ''; // 获取订单号
    $now_time = time();

    // 检查授权类型是否存在
    if (empty($type)) {
        $response = [
            "code" => "400",
            "msg" => "授权类型不能为空"
        ];
    } else {
        // 检查订单号是否存在
        $checkOrderStmt = $conn->prepare("SELECT type FROM orders WHERE out_trade_no = ?");
        $checkOrderStmt->bind_param("s", $out_trade_no);
        $checkOrderStmt->execute();
        $orderResult = $checkOrderStmt->get_result();
        if ($orderResult->num_rows == 0) {
            $response = [
                "code" => "404",
                "msg" => "订单号不存在"
            ];
        } else {
            $row = $orderResult->fetch_assoc();
            $order_type = $row['type'];

            if ($order_type == '1') {
                $response = [
                    "code" => "400",
                    "msg" => "订单已处理"
                ];
            } else {
                // 计算VIP时间
                $vip_time_duration = $type == 1 ? 2592000 : ($type == 2 ? 31536000 : 0); // 1为月卡30天，2为年卡365天，其他为0
                $vip_time = $now_time + $vip_time_duration;

                // 检查是否已经存在该机器人的记录
                $checkStmt = $conn->prepare("SELECT * FROM sq WHERE jqr = ?");
                $checkStmt->bind_param("s", $jqr);
                $checkStmt->execute();
                $result = $checkStmt->get_result();
                $row = $result->fetch_assoc();
                if ($row) {
                    // 如果存在，检查联系人是否一致
                    if ($row['lxr'] != $lxr) {
                        $response = [
                            "code" => "500",
                            "msg" => "授权失败，联系人不一致请联系作者处理"
                        ];
                    } else {
                        // 更新vip_time
                        $updateStmt = $conn->prepare("UPDATE sq SET vip_time = vip_time + ? WHERE jqr = ?");
                        $updateStmt->bind_param("is", $vip_time_duration, $jqr);
                        if ($updateStmt->execute()) {
                            $vip_time = $now_time + $row['vip_time'] + $vip_time_duration;
                            header("Location:../zt.html");
                            $response = [
                                "code" => "200",
                                "msg" => "授权成功",
                                "now_time" => $row['now_time'],
                                "vip_time" => $vip_time
                            ];
                        } else {
                            $response = [
                                "code" => "500",
                                "msg" => "授权失败",
                                "error" => $updateStmt->error
                            ];
                        }
                    }
                } else {
                    // 如果不存在，插入新的记录
                    $insertStmt = $conn->prepare("INSERT INTO sq (jqr, lxr, now_time, vip_time, ty) VALUES (?, ?, ?, ?, ?)");
                    $type = 1;
                    $insertStmt->bind_param("ssiii", $jqr, $lxr, $now_time, $vip_time, $type);
                    header("Location:../zt.html");
                    if ($insertStmt->execute()) {
                        $response = [
                            "code" => "200",
                            "msg" => "授权成功",
                            "now_time" => $now_time,
                            "vip_time" => $vip_time
                        ];
                    } else {
                        $response = [
                            "code" => "500",
                            "msg" => "授权失败",
                            "error" => $insertStmt->error
                        ];
                    }
                }

                // 更新订单状态为已处理
                $updateOrderStmt = $conn->prepare("UPDATE orders SET type = '1' WHERE out_trade_no = ?");
                $updateOrderStmt->bind_param("s", $out_trade_no);
                if (!$updateOrderStmt->execute()) {
                    $response = [
                        "code" => "500",
                        "msg" => "更新订单状态失败",
                        "error" => $updateOrderStmt->error
                    ];
                }
            }
        }
    }

    // 返回 JSON 响应
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
} else {
    // 如果不是 GET 请求，返回错误
    header("HTTP/1.1 405 Method Not Allowed");
    exit("仅支持 GET 请求");
}

// 关闭数据库连接
$conn->close();
?>