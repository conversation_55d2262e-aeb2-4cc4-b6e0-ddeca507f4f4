<?php
// 关闭直接显示错误，但记录它们
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'error.log');

// 设置内容类型为JSON
header('Content-Type: application/json');

// 会话配置
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // 如果使用HTTPS，设置为1

// 启动会话
session_start();

// 检查登录状态
if (!isset($_SESSION['username'])) {
    echo json_encode(array("code" => "401", "error" => "未授权访问"));
    exit();
}

// 数据库配置
include 'config.php';

// 检查数据库连接
if ($conn->connect_error) {
    echo json_encode(array("code" => "500", "error" => "数据库连接失败"));
    exit();
}

// 获取要查询的卡密
$kami = isset($_GET['kami']) ? $conn->real_escape_string($_GET['kami']) : '';

if (empty($kami)) {
    echo json_encode(array("code" => "403", "error" => "参数不全或无效"));
    exit();
}

try {
    // 查询卡密信息
    $sql = "SELECT id, kami, type, valid_days, device_limit, password, remark, new_account, new_password, 
            DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time, 
            DATE_FORMAT(use_time, '%Y-%m-%d %H:%i:%s') as use_time,
            DATE_FORMAT(expiry_time, '%Y-%m-%d %H:%i:%s') as expiry_time
            FROM kami WHERE kami = ?";
    
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        throw new Exception("预处理语句失败: " . $conn->error);
    }
    
    $stmt->bind_param("s", $kami);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $data = $result->fetch_assoc();
        
        // 处理设备限制
        $deviceLimit = $data['device_limit'];
        if ($deviceLimit == '1') {
            $deviceLimitText = '仅限1台电脑';
        } else {
            $deviceLimitText = '独享' . $deviceLimit . '台电脑';
        }
        $data['device_limit_text'] = $deviceLimitText;
        
        // 处理状态
        $status = $data['use_time'] ? "已使用" : "未使用";
        $data['status'] = $status;
        
        echo json_encode(array(
            "code" => "200",
            "msg" => "获取成功",
            "kami" => $data['kami'],
            "type" => $data['type'],
            "valid_days" => $data['valid_days'],
            "device_limit" => $data['device_limit'],
            "password" => $data['password'],
            "remark" => $data['remark'],
            "new_account" => $data['new_account'],
            "new_password" => $data['new_password'],
            "create_time" => $data['create_time'],
            "use_time" => $data['use_time'],
            "expiry_time" => $data['expiry_time'],
            "status" => $status
        ));
    } else {
        echo json_encode(array("code" => "404", "error" => "卡密不存在"));
    }
    
    $stmt->close();
} catch (Exception $e) {
    echo json_encode(array("code" => "500", "error" => $e->getMessage()));
} finally {
    if (isset($conn) && $conn) {
        $conn->close();
    }
}
?> 