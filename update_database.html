<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库更新</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: #e0e5ec;
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        /* 容器样式 */
        .container {
            max-width: 800px;
            width: 100%;
            background-color: #e0e5ec;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 8px 8px 15px rgba(163, 177, 198, 0.5),
                        -8px -8px 15px rgba(255, 255, 255, 0.6);
        }

        h1 {
            font-size: 2rem;
            color: #4CAF50;
            margin-bottom: 20px;
            text-align: center;
        }

        p {
            margin-bottom: 15px;
        }

        .update-container {
            background-color: #e0e5ec;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: inset 5px 5px 10px rgba(163, 177, 198, 0.5),
                        inset -5px -5px 10px rgba(255, 255, 255, 0.6);
        }

        .btn {
            display: inline-block;
            padding: 12px 25px;
            border: none;
            border-radius: 50px;
            background-color: #4CAF50;
            color: white;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.5),
                        -5px -5px 10px rgba(255, 255, 255, 0.6);
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
            margin-top: 20px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 7px 7px 15px rgba(163, 177, 198, 0.5),
                        -7px -7px 15px rgba(255, 255, 255, 0.6);
        }

        .btn:active {
            transform: translateY(0);
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5),
                        -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        .btn i {
            margin-right: 8px;
        }

        .result {
            display: none;
            margin-top: 20px;
        }

        .success {
            background-color: rgba(76, 175, 80, 0.1);
            border-left: 4px solid #4CAF50;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
        }

        .error {
            background-color: rgba(244, 67, 54, 0.1);
            border-left: 4px solid #F44336;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .loading i {
            font-size: 2rem;
            color: #4CAF50;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>数据库更新工具</h1>
        <p>此工具将更新卡密系统数据库结构，添加新账号功能所需的字段。</p>
        
        <div class="update-container">
            <h3><i class="fas fa-info-circle"></i> 更新内容</h3>
            <p>本次更新将添加以下字段：</p>
            <ul style="margin-left: 20px; margin-bottom: 15px;">
                <li>new_account - 用于存储新账号信息</li>
                <li>new_password - 用于存储新账号密码</li>
                <li>expiry_time - 用于存储卡密到期时间（如果尚未添加）</li>
            </ul>
            <p>这些字段将用于支持账号更换功能，允许管理员为已使用的卡密分配新账号。</p>
        </div>
        
        <div class="loading" id="loading">
            <i class="fas fa-spinner"></i>
            <p>正在更新数据库结构，请稍候...</p>
        </div>
        
        <div class="result" id="result"></div>
        
        <div style="text-align: center;">
            <button class="btn" id="update-btn">
                <i class="fas fa-database"></i>
                开始更新数据库
            </button>
            
            <a href="admin.html" class="btn" style="background-color: #2196F3; margin-left: 10px;">
                <i class="fas fa-arrow-left"></i>
                返回管理后台
            </a>
        </div>
    </div>
    
    <script>
        document.getElementById('update-btn').addEventListener('click', function() {
            // 显示加载指示器
            document.getElementById('loading').style.display = 'block';
            // 隐藏结果区域
            document.getElementById('result').style.display = 'none';
            
            // 发送请求
            fetch('api/update_db.php')
                .then(response => response.json())
                .then(data => {
                    // 隐藏加载指示器
                    document.getElementById('loading').style.display = 'none';
                    
                    // 显示结果
                    const resultDiv = document.getElementById('result');
                    resultDiv.style.display = 'block';
                    
                    if (data.code === "200") {
                        let html = '<div class="success">';
                        html += '<h3><i class="fas fa-check-circle"></i> 更新成功</h3>';
                        html += '<p>' + data.msg + '</p>';
                        
                        if (data.updates && data.updates.length > 0) {
                            html += '<ul style="margin-left: 20px; margin-top: 10px;">';
                            data.updates.forEach(update => {
                                html += '<li>' + update + '</li>';
                            });
                            html += '</ul>';
                        }
                        
                        html += '</div>';
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = '<div class="error"><h3><i class="fas fa-times-circle"></i> 更新失败</h3><p>' + (data.error || '未知错误') + '</p></div>';
                    }
                })
                .catch(error => {
                    // 隐藏加载指示器
                    document.getElementById('loading').style.display = 'none';
                    
                    // 显示错误
                    const resultDiv = document.getElementById('result');
                    resultDiv.style.display = 'block';
                    resultDiv.innerHTML = '<div class="error"><h3><i class="fas fa-times-circle"></i> 更新失败</h3><p>请求出错: ' + error.message + '</p></div>';
                });
        });
    </script>
</body>
</html> 