<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.3.0/dist/sweetalert2.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/layui@2.6.8/dist/css/layui.min.css">
    <script>
        // 立即检查登录状态，确保未登录用户不能访问管理页面
        (function() {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'api/check-auth.php', false); // 同步请求，阻塞页面加载
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (!response.isAuthenticated) {
                            window.location.href = 'login.html';
                        }
                    } catch (e) {
                        window.location.href = 'login.html';
                    }
                }
            };
            try {
                xhr.send();
            } catch (e) {
                window.location.href = 'login.html';
            }
        })();
    </script>
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
}

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: #ECF1F7;
            color: #44476a;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
        }

        /* 拟态阴影通用样式 */
        .neumorphic {
            border-radius: 12px;
            background: #ECF1F7;
            box-shadow: 5px 5px 10px #c4c4ca, 
                       -5px -5px 10px #ffffff;
            transition: all 0.3s ease;
        }

        .neumorphic-inset {
            border-radius: 12px;
            background: #ECF1F7;
            box-shadow: inset 3px 3px 6px #c4c4ca, 
                       inset -3px -3px 6px #ffffff;
        }

        /* 侧边栏 */
        .sidebar {
            width: 240px;
            background: #ECF1F7;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            transition: width 0.3s ease;
            z-index: 1000;
            box-shadow: 5px 0 10px rgba(196, 196, 202, 0.5);
            overflow: hidden;
            padding: 20px 0;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            margin-bottom: 20px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            text-align: center;
        }

        .sidebar-header h1 {
            font-size: 24px;
            color: #44476a;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 14px;
            color: #666;
        }

        .menu-item {
            position: relative;
            padding: 16px 20px;
            display: flex;
            align-items: center;
            cursor: pointer;
            color: #44476a;
            transition: all 0.3s;
            margin: 8px 12px;
            border-radius: 8px;
            white-space: nowrap;
        }

        .menu-item:hover {
            background: rgba(68, 71, 106, 0.05);
        }

        .menu-item.active {
            background: #44476a;
            color: white;
            box-shadow: inset 3px 3px 6px rgba(0, 0, 0, 0.2),
                       inset -3px -3px 6px rgba(255, 255, 255, 0.1);
        }

        .menu-item i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 240px;
            padding: 30px;
            width: calc(100% - 240px);
        }

        .content-header {
            margin-bottom: 30px;
        }

        .content-header h2 {
            font-size: 24px;
            color: #44476a;
            margin-bottom: 10px;
        }

        .content-header p {
            color: #666;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            padding: 20px;
            display: flex;
            align-items: center;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #ECF1F7;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            box-shadow: 3px 3px 6px #c4c4ca, 
                       -3px -3px 6px #ffffff;
            font-size: 20px;
        }

        .stat-info h3 {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        .stat-info p {
            font-size: 24px;
            font-weight: 600;
            color: #44476a;
        }

        /* 表格容器 */
        .table-container {
            padding: 20px;
            margin-bottom: 30px;
            overflow-x: auto;
        }

        /* 卡密生成表单 */
        .form-container {
            padding: 20px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #44476a;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: none;
            border-radius: 8px;
            background: #ECF1F7;
            box-shadow: inset 3px 3px 6px #c4c4ca, 
                       inset -3px -3px 6px #ffffff;
            color: #44476a;
            font-size: 16px;
            transition: all 0.3s;
        }

        .form-control:focus {
            outline: none;
            box-shadow: inset 4px 4px 8px #c4c4ca, 
                       inset -4px -4px 8px #ffffff;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            background: #44476a;
            color: white;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 3px 3px 6px #c4c4ca, 
                       -3px -3px 6px #ffffff;
        }

        .btn:hover {
            background: #5a5d7a;
            transform: translateY(-2px);
            box-shadow: 4px 4px 8px #c4c4ca, 
                       -4px -4px 8px #ffffff;
        }

        .btn:active {
            transform: translateY(0);
            box-shadow: 2px 2px 4px #c4c4ca, 
                       -2px -2px 4px #ffffff;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
                padding: 20px 0;
            }

            .sidebar-header {
                padding: 0 10px 20px;
            }

            .sidebar-header h1, .sidebar-header p {
                display: none;
            }

            .menu-item {
                padding: 16px 0;
                justify-content: center;
            }

            .menu-item span {
                display: none;
            }

            .menu-item i {
                margin-right: 0;
                font-size: 20px;
            }

            .main-content {
                margin-left: 70px;
                width: calc(100% - 70px);
                padding: 20px;
            }

            .stats-container {
                grid-template-columns: 1fr;
            }
        }

        /* 隐藏内容 */
        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        /* 表格样式优化 */
        .layui-table {
            margin: 0;
        }

        .layui-table-view {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 3px 3px 6px #c4c4ca, 
                       -3px -3px 6px #ffffff;
        }

        /* 操作按钮 */
        .action-btn {
            padding: 5px 10px;
            border: none;
            border-radius: 4px;
            color: white;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            margin-right: 4px;
            white-space: nowrap;
            display: inline-block;
        }

        .btn-delete {
            background-color: #ff5252;
        }

        .btn-delete:hover {
            background-color: #ff7676;
        }

        .btn-view {
            background-color: #2196F3;
        }

        .btn-view:hover {
            background-color: #42a5f5;
        }
        
        .btn-edit {
            background-color: #4CAF50;
        }
        
        .btn-edit:hover {
            background-color: #66BB6A;
        }

        /* 批量操作区域 */
        .batch-actions {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
            flex-wrap: wrap;
            gap: 10px;
}

        /* 搜索和筛选容器样式 */
        .search-filter-container {
            display: flex;
            margin-left: auto;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-box {
            position: relative;
        }

        .search-box .form-control {
            padding-right: 30px;
            transition: all 0.3s ease;
        }

        .search-box .form-control:focus {
            width: 220px;
        }

        .search-box i {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            pointer-events: none;
        }

        #search-btn {
            background-color: #44476a;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 15px;
            cursor: pointer;
            transition: all 0.3s;
        }

        #search-btn:hover {
            background-color: #5a5d7a;
        }

        @media (max-width: 768px) {
            .search-filter-container {
                margin-left: 0;
                width: 100%;
                margin-top: 10px;
            }
            
            .search-box .form-control,
            #kami-type-filter,
            #kami-status-filter {
                width: 100%;
            }
        }

        /* 编辑卡密弹窗样式 */
        .edit-kami-dialog .swal2-html-container {
            margin: 1em 0;
            overflow-x: hidden;
        }
        
        .edit-kami-dialog .swal2-popup {
            padding: 1.5em;
        }
        
        @media (max-width: 768px) {
            .edit-kami-dialog .swal2-popup {
                width: 95% !important;
                max-width: 95% !important;
                padding: 1em;
            }
        }

        /* 到期时间编辑样式 */
        .layui-table-cell[data-field="expiry_time"] {
            cursor: pointer;
            position: relative;
        }
        
        .layui-table-cell[data-field="expiry_time"]:hover {
            background-color: #f2f2f2;
        }
        
        .layui-table-cell[data-field="expiry_time"]:hover::after {
            content: '点击编辑';
            position: absolute;
            right: 5px;
            font-size: 12px;
            color: #1E9FFF;
            opacity: 0.8;
        }
        
        .edit-expiry-dialog .swal2-html-container {
            margin: 1em 0;
            overflow-x: hidden;
        }

        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input { 
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
        }

        input:checked + .slider {
            background-color: #44476a;
        }

        input:focus + .slider {
            box-shadow: 0 0 1px #44476a;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        /* 圆形滑块 */
        .slider.round {
            border-radius: 34px;
        }

        .slider.round:before {
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h1>卡密管理系统</h1>
            <p>管理您的卡密</p>
        </div>
        <div class="menu-item active" data-section="dashboard">
            <i class="fas fa-chart-line"></i>
            <span>数据统计</span>
        </div>
        <div class="menu-item" data-section="kami-generate">
            <i class="fas fa-plus-circle"></i>
            <span>卡密生成</span>
        </div>
        <div class="menu-item" data-section="kami-manage">
            <i class="fas fa-key"></i>
            <span>卡密管理</span>
        </div>
        <div class="menu-item" data-section="links-manage">
            <i class="fas fa-link"></i>
            <span>购买+客服</span>
        </div>
        <div class="menu-item" onclick="logout()">
            <i class="fas fa-sign-out-alt"></i>
            <span>退出登录</span>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 数据统计 -->
        <div id="dashboard" class="content-section active">
            <div class="content-header">
                <h2>数据统计</h2>
                <p>查看卡密系统的统计数据</p>
                </div>
            <div class="stats-container">
                <div class="stat-card neumorphic">
                    <div class="stat-icon">
                        <i class="fas fa-key" style="color: #4CAF50;"></i>
                    </div>
                    <div class="stat-info">
                        <h3>卡密总数</h3>
                        <p id="total-kami">加载中...</p>
                        </div>
                        </div>
                <div class="stat-card neumorphic">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle" style="color: #2196F3;"></i>
                    </div>
                    <div class="stat-info">
                        <h3>未使用卡密</h3>
                        <p id="unused-kami">加载中...</p>
                </div>
            </div>
                <div class="stat-card neumorphic">
                    <div class="stat-icon">
                        <i class="fas fa-times-circle" style="color: #FF5722;"></i>
                </div>
                    <div class="stat-info">
                        <h3>已使用卡密</h3>
                        <p id="used-kami">加载中...</p>
                    </div>
                    </div>
                </div>
            </div>

        <!-- 卡密生成 -->
        <div id="kami-generate" class="content-section">
            <div class="content-header">
                <h2>卡密生成</h2>
                <p>批量生成卡密</p>
                </div>
            <div class="form-container neumorphic">
                <h3 style="margin-bottom: 20px;">批量生成卡密</h3>
                <div class="form-group">
                    <label for="kami-type">卡密类型</label>
                    <select id="kami-type" class="form-control" onchange="updateValidDays()">
                        <option value="Adobe-全家桶授权-14天">Adobe-全家桶授权-14天</option>
                        <option value="Adobe-全家桶授权-28天">Adobe-全家桶授权-28天</option>
                        <option value="Adobe-全家桶授权-季卡">Adobe-全家桶授权-季卡</option>
                        <option value="Adobe-全家桶授权-半年卡">Adobe-全家桶授权-半年卡</option>
                        <option value="Adobe-全家桶授权-年卡">Adobe-全家桶授权-年卡</option>
                        <option value="自定义类型">自定义类型</option>
                    </select>
                </div>
                <div class="form-group" id="custom-type-container" style="display: none;">
                    <label for="custom-type-name">自定义类型名称</label>
                    <input type="text" id="custom-type-name" class="form-control" placeholder="请输入自定义类型名称">
                </div>
                <div class="form-group">
                    <label for="kami-valid-days">有效天数</label>
                    <input type="number" id="kami-valid-days" class="form-control" min="1" value="14" readonly>
                </div>
                <div class="form-group">
                    <label for="kami-device-limit">可使用设备数量</label>
                    <select id="kami-device-limit" class="form-control">
                        <option value="1">仅限1台电脑</option>
                        <option value="2">独享2台电脑</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="kami-password">密码设置 (可选)</label>
                    <input type="text" id="kami-password" class="form-control" placeholder="留空表示无密码">
                </div>
                <div class="form-group">
                    <label for="kami-remark">备注信息 (可选)</label>
                    <input type="text" id="kami-remark" class="form-control" placeholder="添加备注信息">
                </div>
                <div class="form-group">
                    <label for="kami-content">卡密内容 (每行一个)</label>
                    <textarea id="kami-content" class="form-control" rows="5" placeholder="请输入卡密内容，每行一个卡密&#10;注意：请确保每个卡密不包含额外空格&#10;示例：&#10;card123456&#10;<EMAIL>"></textarea>
                    <small style="display: block; margin-top: 5px; color: #666;">
                        <i class="fas fa-info-circle"></i> 提示：
                        <ul style="margin-top: 5px; padding-left: 20px;">
                            <li>每行输入一个卡密</li>
                            <li>卡密可以包含字母、数字和常见符号</li>
                            <li>请避免使用特殊的控制字符</li>
                            <li>如果卡密包含@符号，请确保格式正确</li>
                        </ul>
                    </small>
                </div>
                <button id="generate-kami-btn" class="btn">生成卡密</button>
            </div>
        </div>

        <!-- 卡密管理 -->
        <div id="kami-manage" class="content-section">
            <div class="content-header">
                <h2>卡密管理</h2>
                <p>查看和管理所有卡密</p>
    </div>
            <div class="table-container neumorphic">
                <div class="batch-actions">
                    <button id="delete-selected-btn" class="btn btn-delete">
                        <i class="fas fa-trash"></i> 删除选中
                    </button>
                    <button id="refresh-table-btn" class="btn">
                        <i class="fas fa-sync-alt"></i> 刷新数据
                    </button>
                    <div class="search-filter-container">
                        <div class="search-box">
                            <input type="text" id="kami-search" placeholder="搜索卡密/备注..." class="form-control" style="width: 200px;">
                            <i class="fas fa-search"></i>
                        </div>
                        <select id="kami-type-filter" class="form-control" style="width: 180px;">
                            <option value="">全部类型</option>
                            <option value="Adobe-全家桶授权-14天">Adobe-全家桶授权-14天</option>
                            <option value="Adobe-全家桶授权-28天">Adobe-全家桶授权-28天</option>
                            <option value="Adobe-全家桶授权-季卡">Adobe-全家桶授权-季卡</option>
                            <option value="Adobe-全家桶授权-半年卡">Adobe-全家桶授权-半年卡</option>
                            <option value="Adobe-全家桶授权-年卡">Adobe-全家桶授权-年卡</option>
                            <option value="自定义类型">自定义类型</option>
                        </select>
                        <select id="kami-status-filter" class="form-control" style="width: 120px;">
                            <option value="">全部状态</option>
                            <option value="0">未使用</option>
                            <option value="1">已使用</option>
                        </select>
                        <button id="search-btn" class="btn">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>
                </div>
                <table id="kami-table" lay-filter="kami-table"></table>
            </div>
        </div>

        <!-- 购买+客服管理 -->
        <div id="links-manage" class="content-section">
            <div class="content-header">
                <h2>导航链接管理</h2>
                <p>管理网站导航链接</p>
            </div>
            <div class="form-container neumorphic">
                <h3 style="margin-bottom: 20px;">编辑导航链接</h3>
                <div style="margin-bottom: 20px; color: #666; background-color: #f8f8f8; padding: 15px; border-radius: 8px;">
                    <p><i class="fas fa-info-circle"></i> 提示：</p>
                    <ul style="margin-top: 5px; margin-left: 20px;">
                        <li>链接将按照排序顺序显示在查询结果页面顶部</li>
                        <li>若不需要某个链接，可以将URL留空或取消勾选"启用"</li>
                        <li>图标使用FontAwesome图标，例如"fa-shopping-cart"（<a href="https://fontawesome.com/v6/search?o=r&m=free" target="_blank" style="color: #1976D2;">查看可用图标</a>）</li>
                        <li>链接样式将根据数量自动调整</li>
                    </ul>
                </div>

                <!-- 主要链接 -->
                <div class="link-section" style="margin-bottom: 30px; padding-bottom: 20px; border-bottom: 1px dashed #ccc;">
                    <h4 style="margin-bottom: 15px; color: #2196F3;">主要链接</h4>
                    
                    <!-- 购买账号链接 -->
                    <div class="link-item" style="margin-bottom: 25px;">
                        <div style="margin-bottom: 10px; display: flex; align-items: center;">
                            <label class="switch" style="margin-right: 10px;">
                                <input type="checkbox" id="buy_account_active" checked>
                                <span class="slider round"></span>
                            </label>
                            <span style="font-weight: 500; font-size: 1.1rem;">购买账号链接</span>
                        </div>
                        <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                            <div style="flex: 3; min-width: 250px;">
                                <label for="buy_account_url" style="display: block; margin-bottom: 5px;">链接地址</label>
                                <input type="text" id="buy_account_url" class="form-control" placeholder="请输入链接地址，如：https://example.com">
                            </div>
                            <div style="flex: 2; min-width: 180px;">
                                <label for="buy_account_display_name" style="display: block; margin-bottom: 5px;">显示名称</label>
                                <input type="text" id="buy_account_display_name" class="form-control" placeholder="购买账号" value="购买账号">
                            </div>
                            <div style="flex: 1; min-width: 140px;">
                                <label for="buy_account_icon" style="display: block; margin-bottom: 5px;">图标</label>
                                <div class="icon-input-container" style="position: relative;">
                                    <input type="text" id="buy_account_icon" class="form-control icon-input" placeholder="fa-shopping-cart" value="fa-shopping-cart">
                                    <div class="icon-preview" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #44476a;">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                </div>
                                <div class="icon-suggestions" style="margin-top: 5px; display: flex; gap: 10px; flex-wrap: wrap;">
                                    <span class="icon-option" data-icon="fa-shopping-cart" style="cursor: pointer; background: #e0e5ec; padding: 4px 8px; border-radius: 4px; box-shadow: 2px 2px 4px rgba(0,0,0,0.1);"><i class="fas fa-shopping-cart"></i></span>
                                    <span class="icon-option" data-icon="fa-cart-plus" style="cursor: pointer; background: #e0e5ec; padding: 4px 8px; border-radius: 4px; box-shadow: 2px 2px 4px rgba(0,0,0,0.1);"><i class="fas fa-cart-plus"></i></span>
                                    <span class="icon-option" data-icon="fa-credit-card" style="cursor: pointer; background: #e0e5ec; padding: 4px 8px; border-radius: 4px; box-shadow: 2px 2px 4px rgba(0,0,0,0.1);"><i class="fas fa-credit-card"></i></span>
                                </div>
                            </div>
                            <div style="flex: 1; min-width: 140px;">
                                <label for="buy_account_color" style="display: block; margin-bottom: 5px;">按钮颜色</label>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <input type="color" id="buy_account_color" class="color-picker" value="#44476a" style="width: 40px; height: 40px; border: none; border-radius: 4px; cursor: pointer; box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5), -3px -3px 6px rgba(255, 255, 255, 0.6);">
                                    <div class="color-preview" style="flex: 1; height: 40px; border-radius: 50px; background-color: #44476a; box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.2), inset -2px -2px 5px rgba(255, 255, 255, 0.1); display: flex; align-items: center; justify-content: center; color: white;">
                                        <i class="fas fa-shopping-cart" style="margin-right: 5px;"></i>
                                        <span>预览</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 问题解答链接 -->
                    <div class="link-item" style="margin-bottom: 25px;">
                        <div style="margin-bottom: 10px; display: flex; align-items: center;">
                            <label class="switch" style="margin-right: 10px;">
                                <input type="checkbox" id="faq_active" checked>
                                <span class="slider round"></span>
                            </label>
                            <span style="font-weight: 500; font-size: 1.1rem;">问题解答链接</span>
                        </div>
                        <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                            <div style="flex: 3; min-width: 250px;">
                                <label for="faq_url" style="display: block; margin-bottom: 5px;">链接地址</label>
                                <input type="text" id="faq_url" class="form-control" placeholder="请输入链接地址，如：https://example.com/faq">
                            </div>
                            <div style="flex: 2; min-width: 180px;">
                                <label for="faq_display_name" style="display: block; margin-bottom: 5px;">显示名称</label>
                                <input type="text" id="faq_display_name" class="form-control" placeholder="问题解答" value="问题解答">
                            </div>
                            <div style="flex: 1; min-width: 140px;">
                                <label for="faq_icon" style="display: block; margin-bottom: 5px;">图标</label>
                                <div class="icon-input-container" style="position: relative;">
                                    <input type="text" id="faq_icon" class="form-control icon-input" placeholder="fa-question-circle" value="fa-question-circle">
                                    <div class="icon-preview" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #44476a;">
                                        <i class="fas fa-question-circle"></i>
                                    </div>
                                </div>
                                <div class="icon-suggestions" style="margin-top: 5px; display: flex; gap: 10px; flex-wrap: wrap;">
                                    <span class="icon-option" data-icon="fa-question-circle" style="cursor: pointer; background: #e0e5ec; padding: 4px 8px; border-radius: 4px; box-shadow: 2px 2px 4px rgba(0,0,0,0.1);"><i class="fas fa-question-circle"></i></span>
                                    <span class="icon-option" data-icon="fa-info-circle" style="cursor: pointer; background: #e0e5ec; padding: 4px 8px; border-radius: 4px; box-shadow: 2px 2px 4px rgba(0,0,0,0.1);"><i class="fas fa-info-circle"></i></span>
                                    <span class="icon-option" data-icon="fa-question" style="cursor: pointer; background: #e0e5ec; padding: 4px 8px; border-radius: 4px; box-shadow: 2px 2px 4px rgba(0,0,0,0.1);"><i class="fas fa-question"></i></span>
                                </div>
                            </div>
                            <div style="flex: 1; min-width: 140px;">
                                <label for="faq_color" style="display: block; margin-bottom: 5px;">按钮颜色</label>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <input type="color" id="faq_color" class="color-picker" value="#2196F3" style="width: 40px; height: 40px; border: none; border-radius: 4px; cursor: pointer; box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5), -3px -3px 6px rgba(255, 255, 255, 0.6);">
                                    <div class="color-preview" style="flex: 1; height: 40px; border-radius: 50px; background-color: #2196F3; box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.2), inset -2px -2px 5px rgba(255, 255, 255, 0.1); display: flex; align-items: center; justify-content: center; color: white;">
                                        <i class="fas fa-question-circle" style="margin-right: 5px;"></i>
                                        <span>预览</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 咨询客服链接 -->
                    <div class="link-item" style="margin-bottom: 25px;">
                        <div style="margin-bottom: 10px; display: flex; align-items: center;">
                            <label class="switch" style="margin-right: 10px;">
                                <input type="checkbox" id="customer_service_active" checked>
                                <span class="slider round"></span>
                            </label>
                            <span style="font-weight: 500; font-size: 1.1rem;">咨询客服链接</span>
                        </div>
                        <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                            <div style="flex: 3; min-width: 250px;">
                                <label for="customer_service_url" style="display: block; margin-bottom: 5px;">链接地址</label>
                                <input type="text" id="customer_service_url" class="form-control" placeholder="请输入链接地址，如：https://example.com/service">
                            </div>
                            <div style="flex: 2; min-width: 180px;">
                                <label for="customer_service_display_name" style="display: block; margin-bottom: 5px;">显示名称</label>
                                <input type="text" id="customer_service_display_name" class="form-control" placeholder="咨询客服" value="咨询客服">
                            </div>
                            <div style="flex: 1; min-width: 140px;">
                                <label for="customer_service_icon" style="display: block; margin-bottom: 5px;">图标</label>
                                <div class="icon-input-container" style="position: relative;">
                                    <input type="text" id="customer_service_icon" class="form-control icon-input" placeholder="fa-headset" value="fa-headset">
                                    <div class="icon-preview" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #44476a;">
                                        <i class="fas fa-headset"></i>
                                    </div>
                                </div>
                                <div class="icon-suggestions" style="margin-top: 5px; display: flex; gap: 10px; flex-wrap: wrap;">
                                    <span class="icon-option" data-icon="fa-headset" style="cursor: pointer; background: #e0e5ec; padding: 4px 8px; border-radius: 4px; box-shadow: 2px 2px 4px rgba(0,0,0,0.1);"><i class="fas fa-headset"></i></span>
                                    <span class="icon-option" data-icon="fa-comments" style="cursor: pointer; background: #e0e5ec; padding: 4px 8px; border-radius: 4px; box-shadow: 2px 2px 4px rgba(0,0,0,0.1);"><i class="fas fa-comments"></i></span>
                                    <span class="icon-option" data-icon="fa-phone" style="cursor: pointer; background: #e0e5ec; padding: 4px 8px; border-radius: 4px; box-shadow: 2px 2px 4px rgba(0,0,0,0.1);"><i class="fas fa-phone"></i></span>
                                </div>
                            </div>
                            <div style="flex: 1; min-width: 140px;">
                                <label for="customer_service_color" style="display: block; margin-bottom: 5px;">按钮颜色</label>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <input type="color" id="customer_service_color" class="color-picker" value="#4CAF50" style="width: 40px; height: 40px; border: none; border-radius: 4px; cursor: pointer; box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5), -3px -3px 6px rgba(255, 255, 255, 0.6);">
                                    <div class="color-preview" style="flex: 1; height: 40px; border-radius: 50px; background-color: #4CAF50; box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.2), inset -2px -2px 5px rgba(255, 255, 255, 0.1); display: flex; align-items: center; justify-content: center; color: white;">
                                        <i class="fas fa-headset" style="margin-right: 5px;"></i>
                                        <span>预览</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 额外链接 -->
                <div class="link-section" style="margin-bottom: 30px;">
                    <h4 style="margin-bottom: 15px; color: #FF9800;">额外链接（最多2个）</h4>
                    
                    <!-- 额外链接1 -->
                    <div class="link-item" style="margin-bottom: 25px;">
                        <div style="margin-bottom: 10px; display: flex; align-items: center;">
                            <label class="switch" style="margin-right: 10px;">
                                <input type="checkbox" id="extra_link_1_active">
                                <span class="slider round"></span>
                            </label>
                            <span style="font-weight: 500; font-size: 1.1rem;">额外链接1</span>
                        </div>
                        <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                            <div style="flex: 3; min-width: 250px;">
                                <label for="extra_link_1_url" style="display: block; margin-bottom: 5px;">链接地址</label>
                                <input type="text" id="extra_link_1_url" class="form-control" placeholder="请输入链接地址，如：https://example.com/extra">
                            </div>
                            <div style="flex: 2; min-width: 180px;">
                                <label for="extra_link_1_display_name" style="display: block; margin-bottom: 5px;">显示名称</label>
                                <input type="text" id="extra_link_1_display_name" class="form-control" placeholder="额外链接1" value="额外链接1">
                            </div>
                            <div style="flex: 1; min-width: 140px;">
                                <label for="extra_link_1_icon" style="display: block; margin-bottom: 5px;">图标</label>
                                <div class="icon-input-container" style="position: relative;">
                                    <input type="text" id="extra_link_1_icon" class="form-control icon-input" placeholder="fa-link" value="fa-link">
                                    <div class="icon-preview" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #44476a;">
                                        <i class="fas fa-link"></i>
                                    </div>
                                </div>
                                <div class="icon-suggestions" style="margin-top: 5px; display: flex; gap: 10px; flex-wrap: wrap;">
                                    <span class="icon-option" data-icon="fa-link" style="cursor: pointer; background: #e0e5ec; padding: 4px 8px; border-radius: 4px; box-shadow: 2px 2px 4px rgba(0,0,0,0.1);"><i class="fas fa-link"></i></span>
                                    <span class="icon-option" data-icon="fa-star" style="cursor: pointer; background: #e0e5ec; padding: 4px 8px; border-radius: 4px; box-shadow: 2px 2px 4px rgba(0,0,0,0.1);"><i class="fas fa-star"></i></span>
                                    <span class="icon-option" data-icon="fa-heart" style="cursor: pointer; background: #e0e5ec; padding: 4px 8px; border-radius: 4px; box-shadow: 2px 2px 4px rgba(0,0,0,0.1);"><i class="fas fa-heart"></i></span>
                                </div>
                            </div>
                            <div style="flex: 1; min-width: 140px;">
                                <label for="extra_link_1_color" style="display: block; margin-bottom: 5px;">按钮颜色</label>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <input type="color" id="extra_link_1_color" class="color-picker" value="#FF9800" style="width: 40px; height: 40px; border: none; border-radius: 4px; cursor: pointer; box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5), -3px -3px 6px rgba(255, 255, 255, 0.6);">
                                    <div class="color-preview" style="flex: 1; height: 40px; border-radius: 50px; background-color: #FF9800; box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.2), inset -2px -2px 5px rgba(255, 255, 255, 0.1); display: flex; align-items: center; justify-content: center; color: white;">
                                        <i class="fas fa-link" style="margin-right: 5px;"></i>
                                        <span>预览</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 额外链接2 -->
                    <div class="link-item" style="margin-bottom: 25px;">
                        <div style="margin-bottom: 10px; display: flex; align-items: center;">
                            <label class="switch" style="margin-right: 10px;">
                                <input type="checkbox" id="extra_link_2_active">
                                <span class="slider round"></span>
                            </label>
                            <span style="font-weight: 500; font-size: 1.1rem;">额外链接2</span>
                        </div>
                        <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                            <div style="flex: 3; min-width: 250px;">
                                <label for="extra_link_2_url" style="display: block; margin-bottom: 5px;">链接地址</label>
                                <input type="text" id="extra_link_2_url" class="form-control" placeholder="请输入链接地址，如：https://example.com/extra2">
                            </div>
                            <div style="flex: 2; min-width: 180px;">
                                <label for="extra_link_2_display_name" style="display: block; margin-bottom: 5px;">显示名称</label>
                                <input type="text" id="extra_link_2_display_name" class="form-control" placeholder="额外链接2" value="额外链接2">
                            </div>
                            <div style="flex: 1; min-width: 140px;">
                                <label for="extra_link_2_icon" style="display: block; margin-bottom: 5px;">图标</label>
                                <div class="icon-input-container" style="position: relative;">
                                    <input type="text" id="extra_link_2_icon" class="form-control icon-input" placeholder="fa-link" value="fa-link">
                                    <div class="icon-preview" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #44476a;">
                                        <i class="fas fa-link"></i>
                                    </div>
                                </div>
                                <div class="icon-suggestions" style="margin-top: 5px; display: flex; gap: 10px; flex-wrap: wrap;">
                                    <span class="icon-option" data-icon="fa-link" style="cursor: pointer; background: #e0e5ec; padding: 4px 8px; border-radius: 4px; box-shadow: 2px 2px 4px rgba(0,0,0,0.1);"><i class="fas fa-link"></i></span>
                                    <span class="icon-option" data-icon="fa-gift" style="cursor: pointer; background: #e0e5ec; padding: 4px 8px; border-radius: 4px; box-shadow: 2px 2px 4px rgba(0,0,0,0.1);"><i class="fas fa-gift"></i></span>
                                    <span class="icon-option" data-icon="fa-bell" style="cursor: pointer; background: #e0e5ec; padding: 4px 8px; border-radius: 4px; box-shadow: 2px 2px 4px rgba(0,0,0,0.1);"><i class="fas fa-bell"></i></span>
                                </div>
                            </div>
                            <div style="flex: 1; min-width: 140px;">
                                <label for="extra_link_2_color" style="display: block; margin-bottom: 5px;">按钮颜色</label>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <input type="color" id="extra_link_2_color" class="color-picker" value="#9C27B0" style="width: 40px; height: 40px; border: none; border-radius: 4px; cursor: pointer; box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5), -3px -3px 6px rgba(255, 255, 255, 0.6);">
                                    <div class="color-preview" style="flex: 1; height: 40px; border-radius: 50px; background-color: #9C27B0; box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.2), inset -2px -2px 5px rgba(255, 255, 255, 0.1); display: flex; align-items: center; justify-content: center; color: white;">
                                        <i class="fas fa-link" style="margin-right: 5px;"></i>
                                        <span>预览</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <button id="save-links-btn" class="btn">保存链接设置</button>
            </div>
        </div>
        </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/layui@2.6.8/dist/layui.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.3.0/dist/sweetalert2.min.js"></script>
    <script>
        // 检查登录状态 - 用于功能调用中检查
        function checkLogin() {
            return fetch('api/check-auth.php')
                .then(response => response.json())
                .then(data => {
                    if (!data.isAuthenticated) {
                        window.location.href = 'login.html';
                        return false;
                    }
                    return true;
                })
                .catch(() => {
                    window.location.href = 'login.html';
                    return false;
                });
        }

        // 根据卡密类型更新有效天数
        function updateValidDays() {
            const kamiType = document.getElementById('kami-type').value;
            const validDaysInput = document.getElementById('kami-valid-days');
            const customTypeContainer = document.getElementById('custom-type-container');
            
            // 根据选择的卡密类型设置对应的有效天数
            switch(kamiType) {
                case 'Adobe-全家桶授权-14天':
                    validDaysInput.value = 14;
                    validDaysInput.readOnly = true;
                    customTypeContainer.style.display = 'none';
                    break;
                case 'Adobe-全家桶授权-28天':
                    validDaysInput.value = 28;
                    validDaysInput.readOnly = true;
                    customTypeContainer.style.display = 'none';
                    break;
                case 'Adobe-全家桶授权-季卡':
                    validDaysInput.value = 90;
                    validDaysInput.readOnly = true;
                    customTypeContainer.style.display = 'none';
                    break;
                case 'Adobe-全家桶授权-半年卡':
                    validDaysInput.value = 180;
                    validDaysInput.readOnly = true;
                    customTypeContainer.style.display = 'none';
                    break;
                case 'Adobe-全家桶授权-年卡':
                    validDaysInput.value = 365;
                    validDaysInput.readOnly = true;
                    customTypeContainer.style.display = 'none';
                    break;
                case '自定义类型':
                    validDaysInput.value = '';
                    validDaysInput.readOnly = false;
                    customTypeContainer.style.display = 'block';
                    break;
            }
        }

        // 退出登录
        function logout() {
            fetch('api/logout.php')
                .then(() => {
                    window.location.href = 'login.html';
                })
                .catch(error => {
                    console.error('退出登录失败:', error);
                    window.location.href = 'login.html';
                });
        }

        // 加载统计数据
        function loadStats() {
            fetch('api/ui.php')
        .then(response => response.json())
        .then(data => {
                    document.getElementById('total-kami').textContent = data.kami.total;
                    document.getElementById('unused-kami').textContent = data.kami.zt0;
                    document.getElementById('used-kami').textContent = data.kami.zt1;
        })
        .catch(error => {
                    console.error('加载统计数据失败:', error);
                    Swal.fire({
                        title: '加载失败',
                        text: '无法加载统计数据，请稍后再试',
                        icon: 'error'
                    });
                });
        }

        // 导航菜单切换
        document.querySelectorAll('.menu-item').forEach(item => {
            if (!item.getAttribute('onclick')) {
                item.addEventListener('click', function() {
                    // 更新菜单激活状态
                    document.querySelectorAll('.menu-item').forEach(menuItem => {
                        menuItem.classList.remove('active');
                    });
                    this.classList.add('active');

                    // 显示对应内容区域
                    const sectionId = this.getAttribute('data-section');
                    document.querySelectorAll('.content-section').forEach(section => {
                        section.classList.remove('active');
                    });
                    document.getElementById(sectionId).classList.add('active');
                });
            }
        });

        // 初始化Layui表格
        function initTable() {
            layui.use('table', function() {
                const table = layui.table;
                
                table.render({
                    elem: '#kami-table',
                    url: 'api/kamilist.php',
                    page: true,
                    limit: 10,
                    limits: [10, 20, 50, 100],
                    where: {
                        search: '',
                        type: '',
                        status: ''
                    },
                    cols: [[
                        {type: 'checkbox', fixed: 'left'},
                        {field: 'id', title: 'ID', width: 60, sort: true},
                        {field: 'kami', title: '卡密', width: 220, overflow: 'text'},
                        {field: 'type', title: '类型', width: 180, overflow: 'text'},
                        {field: 'valid_days', title: '有效天数', width: 100},
                        {field: 'device_limit', title: '设备数量', width: 100, templet: function(d) {
                            return d.device_limit == '1' ? '仅限1台' : '独享2台';
                        }},
                        {field: 'status', title: '状态', width: 80, templet: function(d) {
                            return d.use_time ? '<span style="color: #FF5722;">已使用</span>' : '<span style="color: #2196F3;">未使用</span>';
                        }},
                        {field: 'password', title: '密码', width: 100},
                        {field: 'new_account', title: '新账号', width: 150, edit: 'text', templet: function(d) {
                            return d.new_account ? d.new_account : '<span style="color: #999;">无</span>';
                        }},
                        {field: 'new_password', title: '新密码', width: 150, edit: 'text', templet: function(d) {
                            return d.new_password ? d.new_password : '<span style="color: #999;">无</span>';
                        }},
                        {field: 'remark', title: '备注', width: 120},
                        {field: 'create_time', title: '创建时间', width: 160},
                        {field: 'use_time', title: '使用时间', width: 160},
                        {field: 'expiry_time', title: '到期时间', width: 160, templet: function(d) {
                            return d.expiry_time ? d.expiry_time.split(' ')[0] : '';
                        }, event: 'editExpiryTime'},
                        {title: '操作', width: 180, templet: function() {
                            return '<button class="action-btn btn-edit" lay-event="edit"><i class="fas fa-edit"></i>编辑</button>' +
                                   '<button class="action-btn btn-delete" lay-event="del"><i class="fas fa-trash"></i>删除</button>';
                        }}
                    ]],
                    response: {
                        statusCode: 200
                    },
                    parseData: function(res) {
                        return {
                            "code": res.code,
                            "msg": res.msg,
                            "count": res.count,
                            "data": res.data
                        };
                    }
                });

                // 监听表格操作
                table.on('tool(kami-table)', function(obj) {
                    const data = obj.data;
                    if (obj.event === 'del') {
                        Swal.fire({
                            title: '确认删除',
                            text: `确定要删除卡密 "${data.kami}" 吗？`,
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonText: '确定删除',
                            cancelButtonText: '取消'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                deleteKami(data.kami);
                            }
                        });
                    } else if (obj.event === 'edit') {
                        // 编辑卡密
                        editKami(data);
                    } else if (obj.event === 'editExpiryTime') {
                        // 编辑到期时间
                        editExpiryTime(data);
                    }
                });
                
                // 监听单元格编辑事件
                table.on('edit(kami-table)', function(obj){
                    const value = obj.value; // 得到修改后的值
                    const field = obj.field; // 得到字段
                    const data = obj.data; // 得到所在行所有键值
                    
                    // 显示加载中提示
                    const loadingIndex = layer.msg('正在保存...', {
                        icon: 16,
                        time: 0,
                        shade: 0.3
                    });
                    
                    // 准备要发送的数据
                    const formData = new FormData();
                    formData.append('kami', data.kami);
                    formData.append('type', data.type);
                    formData.append('valid_days', data.valid_days);
                    formData.append('device_limit', data.device_limit);
                    formData.append('password', data.password || '');
                    formData.append('remark', data.remark || '');
                    
                    // 根据编辑的字段设置相应的值
                    if (field === 'new_account') {
                        formData.append('field', 'new_account');
                        formData.append('new_account', value || '');
                        formData.append('new_password', data.new_password || '');
                    } else if (field === 'new_password') {
                        formData.append('field', 'new_password');
                        formData.append('new_account', data.new_account || '');
                        formData.append('new_password', value || '');
                    }
                    
                    // 发送请求更新数据
                    fetch('api/update_kami.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(result => {
                        layer.close(loadingIndex);
                        if (result.code === "200") {
                            layer.msg('修改成功', {icon: 1});
                            // 刷新表格，确保数据一致性
                            refreshTable();
                        } else {
                            layer.msg('修改失败: ' + (result.error || '未知错误'), {icon: 2});
                            // 刷新表格，恢复原始数据
                            refreshTable();
                        }
                    })
                    .catch(error => {
                        layer.close(loadingIndex);
                        console.error('更新卡密失败:', error);
                        layer.msg('请求出错，请稍后再试', {icon: 2});
                        // 刷新表格，恢复原始数据
                        refreshTable();
                    });
                });
            });
        }

        // 删除卡密
        function deleteKami(kami) {
            fetch(`api/delete_kami.php?kamiList=${encodeURIComponent(kami)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === "200") {
                        Swal.fire({
                            title: '删除成功',
                            text: data.msg,
                            icon: 'success'
                        }).then(() => {
                            refreshTable();
                            loadStats();
                        });
                } else {
                        Swal.fire({
                            title: '删除失败',
                            text: data.error || '操作失败，请稍后再试',
                            icon: 'error'
                        });
                    }
                })
                .catch(error => {
                    console.error('删除卡密失败:', error);
                    Swal.fire({
                        title: '删除失败',
                        text: '请求出错，请稍后再试',
                        icon: 'error'
                    });
                });
        }

        // 编辑卡密
        function editKami(data) {
            // 显示加载中提示
            const loadingIndex = layer.msg('加载卡密信息...', {
                icon: 16,
                time: 0,
                shade: 0.3
            });
            
            // 先获取最新的卡密信息
            fetch(`api/get_kami_info.php?kami=${encodeURIComponent(data.kami)}`)
                .then(response => response.json())
                .then(result => {
                    layer.close(loadingIndex);
                    
                    if (result.code === "200") {
                        // 使用最新的数据
                        showEditDialog(result);
                    } else {
                        layer.msg('获取卡密信息失败: ' + (result.error || '未知错误'), {icon: 2});
                    }
                })
                .catch(error => {
                    layer.close(loadingIndex);
                    console.error('获取卡密信息失败:', error);
                    layer.msg('请求出错，请稍后再试', {icon: 2});
                });
        }
        
        // 编辑到期时间
        function editExpiryTime(data) {
            // 格式化到期时间（如果有）
            let expiryTimeValue = '';
            let expiryTimeHours = '23';
            let expiryTimeMinutes = '59';
            let expiryTimeSeconds = '59';
            
            if (data.expiry_time) {
                // 如果已有到期时间，格式化为YYYY-MM-DD
                const parts = data.expiry_time.split(' ');
                expiryTimeValue = parts[0]; // 只取日期部分
                
                // 保存时间部分用于后续处理
                if (parts.length > 1 && parts[1]) {
                    const timeParts = parts[1].split(':');
                    if (timeParts.length >= 3) {
                        expiryTimeHours = timeParts[0];
                        expiryTimeMinutes = timeParts[1];
                        expiryTimeSeconds = timeParts[2];
                    }
                }
            }

            Swal.fire({
                title: '编辑到期时间',
                width: '500px',
                customClass: {
                    container: 'edit-expiry-dialog'
                },
                html: `
                    <style>
                        .edit-expiry-form {
                            text-align: left;
                            width: 100%;
                        }
                        .edit-expiry-form .form-row {
                            margin-bottom: 15px;
                        }
                        .edit-expiry-form label {
                            display: block;
                            margin-bottom: 5px;
                            font-weight: 500;
                        }
                        .edit-expiry-form input {
                            width: 100%;
                            padding: 8px 10px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-sizing: border-box;
                        }
                        .edit-expiry-form .time-inputs {
                            display: flex;
                            align-items: center;
                            gap: 5px;
                        }
                        .edit-expiry-form .time-inputs input {
                            width: 60px;
                            text-align: center;
                        }
                        .edit-expiry-form small {
                            display: block;
                            margin-top: 5px;
                            color: #666;
                            font-size: 12px;
                        }
                    </style>
                    <div class="edit-expiry-form">
                        <div class="form-row">
                            <label><strong>卡密:</strong> ${data.kami}</label>
                        </div>
                        
                        <div class="form-row">
                            <label for="edit-expiry-date">到期日期</label>
                            <input id="edit-expiry-date" type="date" value="${expiryTimeValue}">
                        </div>
                        
                        <div class="form-row">
                            <label>到期时间（时:分:秒）</label>
                            <div class="time-inputs">
                                <input id="edit-expiry-hours" type="number" min="0" max="23" value="${expiryTimeHours}">
                                <span>:</span>
                                <input id="edit-expiry-minutes" type="number" min="0" max="59" value="${expiryTimeMinutes}">
                                <span>:</span>
                                <input id="edit-expiry-seconds" type="number" min="0" max="59" value="${expiryTimeSeconds}">
                            </div>
                            <small><i class="fas fa-info-circle"></i> 设置到期时间将优先使用到期时间而不是有效天数计算</small>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '保存',
                cancelButtonText: '取消',
                preConfirm: () => {
                    const expiryDate = document.getElementById('edit-expiry-date').value;
                    const expiryHours = document.getElementById('edit-expiry-hours').value.padStart(2, '0');
                    const expiryMinutes = document.getElementById('edit-expiry-minutes').value.padStart(2, '0');
                    const expirySeconds = document.getElementById('edit-expiry-seconds').value.padStart(2, '0');
                    
                    if (!expiryDate) {
                        Swal.showValidationMessage('请选择到期日期');
                        return false;
                    }
                    
                    // 组合日期和时间
                    const expiryTime = `${expiryDate} ${expiryHours}:${expiryMinutes}:${expirySeconds}`;
                    return { expiryTime };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // 显示加载中提示
                    const loadingIndex = layer.msg('正在保存...', {
                        icon: 16,
                        time: 0,
                        shade: 0.3
                    });
                    
                    // 准备要发送的数据
                    const formData = new FormData();
                    formData.append('kami', data.kami);
                    formData.append('type', data.type);
                    formData.append('valid_days', data.valid_days);
                    formData.append('device_limit', data.device_limit);
                    formData.append('password', data.password || '');
                    formData.append('remark', data.remark || '');
                    formData.append('field', 'expiry_time');
                    formData.append('expiry_time', result.value.expiryTime);
                    formData.append('new_account', data.new_account || '');
                    formData.append('new_password', data.new_password || '');
                    
                    // 发送请求更新数据
                    fetch('api/update_kami.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(result => {
                        layer.close(loadingIndex);
                        if (result.code === "200") {
                            layer.msg('修改成功', {icon: 1});
                            // 刷新表格，确保数据一致性
                            refreshTable();
                        } else {
                            layer.msg('修改失败: ' + (result.error || '未知错误'), {icon: 2});
                            // 刷新表格，恢复原始数据
                            refreshTable();
                        }
                    })
                    .catch(error => {
                        layer.close(loadingIndex);
                        console.error('更新卡密失败:', error);
                        layer.msg('请求出错，请稍后再试', {icon: 2});
                        // 刷新表格，恢复原始数据
                        refreshTable();
                    });
                }
            });
        }
        
        // 显示编辑卡密对话框
        function showEditDialog(data) {
            // 根据卡密类型设置有效天数的只读状态和自定义类型名称
            let isCustomType = !data.type.startsWith('Adobe-全家桶授权-');
            let customTypeName = isCustomType ? data.type : '';
            let deviceLimitValue = data.device_limit || '1';

            // 格式化到期时间（如果有）
            let expiryTimeValue = '';
            let expiryTimeHours = '23';
            let expiryTimeMinutes = '59';
            let expiryTimeSeconds = '59';

            if (data.expiry_time) {
                // 如果已有到期时间，格式化为YYYY-MM-DD
                const parts = data.expiry_time.split(' ');
                expiryTimeValue = parts[0]; // 只取日期部分

                // 保存时间部分用于后续处理
                if (parts.length > 1 && parts[1]) {
                    const timeParts = parts[1].split(':');
                    if (timeParts.length >= 3) {
                        expiryTimeHours = timeParts[0];
                        expiryTimeMinutes = timeParts[1];
                        expiryTimeSeconds = timeParts[2];
                    }
                }
            }

            Swal.fire({
                title: '编辑卡密',
                width: '800px',
                customClass: {
                    container: 'edit-kami-dialog'
                },
                html: `
                    <style>
                        .edit-form-container {
                            text-align: left;
                            width: 100%;
                        }
                        .edit-form-container .form-row {
                            margin-bottom: 15px;
                        }
                        .edit-form-container label {
                            display: block;
                            margin-bottom: 5px;
                            font-weight: 500;
                        }
                        .edit-form-container input, 
                        .edit-form-container select {
                            width: 100%;
                            padding: 8px 10px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-sizing: border-box;
                        }
                        .edit-form-container .form-group {
                            margin-bottom: 20px;
                        }
                        .edit-form-container .form-group-title {
                            font-weight: bold;
                            margin: 15px 0 10px;
                            padding-bottom: 5px;
                            border-bottom: 1px dashed #ccc;
                        }
                        .edit-form-container .form-flex {
                            display: flex;
                            gap: 15px;
                        }
                        .edit-form-container .form-flex .form-col {
                            flex: 1;
                            min-width: 0;
                        }
                        .edit-form-container .time-inputs {
                            display: flex;
                            align-items: center;
                            gap: 5px;
                        }
                        .edit-form-container .time-inputs input {
                            width: 60px;
                            text-align: center;
                        }
                        .edit-form-container small {
                            display: block;
                            margin-top: 5px;
                            color: #666;
                            font-size: 12px;
                        }
                        .edit-form-container .warning-section {
                            margin-top: 15px;
                            padding-top: 15px;
                            border-top: 1px dashed #ccc;
                        }
                        .edit-form-container .warning-title {
                            font-weight: bold;
                            color: #FF9800;
                            margin-bottom: 10px;
                        }
                    </style>
                    <div class="edit-form-container">
                        <div class="form-row">
                            <label><strong>卡密:</strong> ${data.kami}</label>
                        </div>
                        
                        <div class="form-group">
                            <div class="form-row">
                                <label for="edit-type">卡密类型</label>
                                <select id="edit-type" onchange="updateEditValidDays()">
                                    <option value="Adobe-全家桶授权-14天" ${data.type === 'Adobe-全家桶授权-14天' ? 'selected' : ''}>Adobe-全家桶授权-14天</option>
                                    <option value="Adobe-全家桶授权-28天" ${data.type === 'Adobe-全家桶授权-28天' ? 'selected' : ''}>Adobe-全家桶授权-28天</option>
                                    <option value="Adobe-全家桶授权-季卡" ${data.type === 'Adobe-全家桶授权-季卡' ? 'selected' : ''}>Adobe-全家桶授权-季卡</option>
                                    <option value="Adobe-全家桶授权-半年卡" ${data.type === 'Adobe-全家桶授权-半年卡' ? 'selected' : ''}>Adobe-全家桶授权-半年卡</option>
                                    <option value="Adobe-全家桶授权-年卡" ${data.type === 'Adobe-全家桶授权-年卡' ? 'selected' : ''}>Adobe-全家桶授权-年卡</option>
                                    <option value="自定义类型" ${isCustomType ? 'selected' : ''}>自定义类型</option>
                                </select>
                            </div>

                            <div class="form-row" id="edit-custom-type-container" style="display: ${isCustomType ? 'block' : 'none'};">
                                <label for="edit-custom-type-name">自定义类型名称</label>
                                <input id="edit-custom-type-name" type="text" value="${customTypeName}" placeholder="请输入自定义类型名称">
                            </div>
                            
                            <div class="form-row">
                                <label for="edit-valid-days">有效天数</label>
                                <input id="edit-valid-days" type="number" min="1" value="${data.valid_days || 14}" ${!isCustomType ? 'readonly' : ''}>
                            </div>
                            
                            <div class="form-row">
                                <label for="edit-device-limit">可使用设备数量</label>
                                <select id="edit-device-limit">
                                    <option value="1" ${deviceLimitValue === '1' ? 'selected' : ''}>仅限1台电脑</option>
                                    <option value="2" ${deviceLimitValue === '2' ? 'selected' : ''}>独享2台电脑</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="form-group-title">到期时间设置</div>
                            <div class="form-row">
                                <label for="edit-expiry-time">到期日期（可选）</label>
                                <input id="edit-expiry-time" type="date" value="${expiryTimeValue}">
                            </div>
                            
                            <div class="form-row">
                                <label>到期时间（时:分:秒）</label>
                                <div class="time-inputs">
                                    <input id="edit-expiry-hours" type="number" min="0" max="23" value="${expiryTimeHours}">
                                    <span>:</span>
                                    <input id="edit-expiry-minutes" type="number" min="0" max="59" value="${expiryTimeMinutes}">
                                    <span>:</span>
                                    <input id="edit-expiry-seconds" type="number" min="0" max="59" value="${expiryTimeSeconds}">
                                </div>
                                <small><i class="fas fa-info-circle"></i> 设置到期时间将优先使用到期时间而不是有效天数计算</small>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="form-group-title">附加信息</div>
                            <div class="form-flex">
                                <div class="form-col">
                                    <label for="edit-password">密码设置 (可选)</label>
                                    <input id="edit-password" type="text" value="${data.password || ''}" placeholder="留空表示无密码">
                                </div>
                                <div class="form-col">
                                    <label for="edit-remark">备注信息 (可选)</label>
                                    <input id="edit-remark" type="text" value="${data.remark || ''}" placeholder="添加备注信息">
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group warning-section">
                            <div class="warning-title">
                                <i class="fas fa-exclamation-triangle" style="margin-right: 5px;"></i>
                                新账号设置（仅在需要更换账号时填写）
                            </div>
                            <div class="form-flex">
                                <div class="form-col">
                                    <label for="edit-new-account">新账号</label>
                                    <input id="edit-new-account" type="text" value="${data.new_account || ''}" placeholder="输入新账号">
                                </div>
                                <div class="form-col">
                                    <label for="edit-new-password">新密码</label>
                                    <input id="edit-new-password" type="text" value="${data.new_password || ''}" placeholder="输入新密码">
                                    <small><i class="fas fa-info-circle"></i> 如不设置新密码，将默认使用原密码</small>
                                </div>
                            </div>
                            <small><i class="fas fa-info-circle"></i> 设置新账号后，查询结果页面将显示新账号信息，原账号将变为不可用状态</small>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '保存',
                cancelButtonText: '取消',
                didOpen: () => {
                    // 添加脚本以根据卡密类型更新有效天数的只读状态和值
                    window.updateEditValidDays = function() {
                        const typeSelect = document.getElementById('edit-type');
                        const validDaysInput = document.getElementById('edit-valid-days');
                        const customTypeContainer = document.getElementById('edit-custom-type-container');

                        if (typeSelect.value === 'Adobe-全家桶授权-14天') {
                            validDaysInput.value = 14;
                            validDaysInput.readOnly = true;
                            customTypeContainer.style.display = 'none';
                        } else if (typeSelect.value === 'Adobe-全家桶授权-28天') {
                            validDaysInput.value = 28;
                            validDaysInput.readOnly = true;
                            customTypeContainer.style.display = 'none';
                        } else if (typeSelect.value === 'Adobe-全家桶授权-季卡') {
                            validDaysInput.value = 90;
                            validDaysInput.readOnly = true;
                            customTypeContainer.style.display = 'none';
                        } else if (typeSelect.value === 'Adobe-全家桶授权-半年卡') {
                            validDaysInput.value = 180;
                            validDaysInput.readOnly = true;
                            customTypeContainer.style.display = 'none';
                        } else if (typeSelect.value === 'Adobe-全家桶授权-年卡') {
                            validDaysInput.value = 365;
                            validDaysInput.readOnly = true;
                            customTypeContainer.style.display = 'none';
                        } else if (typeSelect.value === '自定义类型') {
                            validDaysInput.readOnly = false;
                            customTypeContainer.style.display = 'block';
                        }
                    };
                },
                preConfirm: () => {
                    let type = document.getElementById('edit-type').value;
                    const validDays = document.getElementById('edit-valid-days').value;
                    const expiryDate = document.getElementById('edit-expiry-time').value;
                    const expiryHours = document.getElementById('edit-expiry-hours').value.padStart(2, '0');
                    const expiryMinutes = document.getElementById('edit-expiry-minutes').value.padStart(2, '0');
                    const expirySeconds = document.getElementById('edit-expiry-seconds').value.padStart(2, '0');
                    // 组合日期和时间
                    const expiryTime = expiryDate ? `${expiryDate} ${expiryHours}:${expiryMinutes}:${expirySeconds}` : '';
                    const deviceLimit = document.getElementById('edit-device-limit').value;
                    const password = document.getElementById('edit-password').value;
                    const remark = document.getElementById('edit-remark').value;
                    const newAccount = document.getElementById('edit-new-account').value;
                    const newPassword = document.getElementById('edit-new-password').value;

                    // 如果选择了自定义类型，使用自定义类型名称
                    if (type === '自定义类型') {
                        const customTypeName = document.getElementById('edit-custom-type-name').value.trim();
                        if (!customTypeName) {
                            Swal.showValidationMessage('请输入自定义类型名称');
                            return false;
                        }
                        type = customTypeName;
                    }

                    if (!type || !validDays || validDays < 1) {
                        Swal.showValidationMessage('请选择卡密类型并输入有效的天数');
                        return false;
                    }

                    return {
                        kami: data.kami,
                        type: type,
                        valid_days: validDays,
                        expiry_time: expiryTime,
                        device_limit: deviceLimit,
                        password: password,
                        remark: remark,
                        new_account: newAccount,
                        new_password: newPassword
                    };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    const formData = new FormData();
                    formData.append('kami', result.value.kami);
                    formData.append('type', result.value.type);
                    formData.append('valid_days', result.value.valid_days);
                    formData.append('expiry_time', result.value.expiry_time);
                    formData.append('device_limit', result.value.device_limit);
                    formData.append('password', result.value.password);
                    formData.append('remark', result.value.remark);
                    formData.append('new_account', result.value.new_account);
                    formData.append('new_password', result.value.new_password);
                    
                    fetch('api/update_kami.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === "200") {
                            Swal.fire({
                                title: '更新成功',
                                text: '卡密信息已成功更新',
                                icon: 'success'
                            }).then(() => {
                                refreshTable();
                            });
                        } else {
                            Swal.fire({
                                title: '更新失败',
                                text: data.error || '操作失败，请稍后再试',
                                icon: 'error'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('更新卡密失败:', error);
                        Swal.fire({
                            title: '更新失败',
                            text: '请求出错，请稍后再试',
                            icon: 'error'
                        });
                    });
                }
            });
        }

        // 刷新表格数据
        function refreshTable() {
            const searchValue = document.getElementById('kami-search').value;
            const typeValue = document.getElementById('kami-type-filter').value;
            const statusValue = document.getElementById('kami-status-filter').value;
            
            layui.table.reload('kami-table', {
                where: {
                    search: searchValue,
                    type: typeValue,
                    status: statusValue
                },
                page: {
                    curr: 1 // 重置到第一页
                }
            });
        }

        // 批量生成卡密
        document.getElementById('generate-kami-btn').addEventListener('click', function() {
            let type = document.getElementById('kami-type').value;
            const validDays = document.getElementById('kami-valid-days').value;
            const deviceLimit = document.getElementById('kami-device-limit').value;
            const password = document.getElementById('kami-password').value;
            const remark = document.getElementById('kami-remark').value;
            const kamiContent = document.getElementById('kami-content').value;
            
            // 如果选择了自定义类型，则使用用户输入的自定义类型名称
            if (type === '自定义类型') {
                const customTypeName = document.getElementById('custom-type-name').value.trim();
                if (customTypeName) {
                    type = customTypeName;
                } else {
                    Swal.fire({
                        title: '参数错误',
                        text: '请输入自定义类型名称',
                        icon: 'error'
                    });
                    return;
                }
            }
            
            // 验证输入
            if (!type || !validDays || validDays < 1) {
                Swal.fire({
                    title: '参数错误',
                    text: '请选择卡密类型并输入有效的天数',
                    icon: 'error'
                });
                return;
            }
            
            if (!kamiContent.trim()) {
                Swal.fire({
                    title: '参数错误',
                    text: '请输入卡密内容',
                    icon: 'error'
                });
                return;
            }

            Swal.fire({
                title: '正在生成',
                text: '正在批量生成卡密，请稍候...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                    
                    // 准备要发送的数据
                    const formData = new FormData();
                    formData.append('type', type);
                    formData.append('valid_days', validDays);
                    formData.append('device_limit', deviceLimit);
                    formData.append('password', password);
                    formData.append('remark', remark);
                    formData.append('kami_content', kamiContent);
                    
                    fetch('api/add_kami.php', {
                        method: 'POST',
                        body: formData
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.code === "200") {
                                Swal.fire({
                                    title: '生成成功',
                                    text: `成功生成 ${data.count} 个卡密`,
                                    icon: 'success'
                                }).then(() => {
                                    refreshTable();
                                    loadStats();
                                    // 清空输入框
                                    document.getElementById('kami-content').value = '';
                                    document.getElementById('kami-password').value = '';
                                    document.getElementById('kami-remark').value = '';
                                });
                            } else if (data.code === "206") {
                                // 部分成功
                                let message = data.msg;
                                let failedKamisHtml = '';
                                
                                if (data.failed_kamis && data.failed_kamis.length > 0) {
                                    failedKamisHtml = '<div style="text-align: left; margin-top: 10px;"><strong>失败的卡密：</strong><pre style="max-height: 150px; overflow-y: auto; background: #f5f5f5; padding: 10px; border-radius: 5px; margin-top: 5px;">' + 
                                        data.failed_kamis.join('\n') + '</pre></div>';
                                }
                                
                                Swal.fire({
                                    title: '部分成功',
                                    html: message + failedKamisHtml,
                                    icon: 'warning'
                                }).then(() => {
                                    refreshTable();
                                    loadStats();
                                    // 清空输入框
                                    document.getElementById('kami-content').value = '';
                                    document.getElementById('kami-password').value = '';
                                    document.getElementById('kami-remark').value = '';
                                });
                            } else {
                                let errorMsg = data.error || '操作失败，请稍后再试';
                                let failedKamisHtml = '';
                                
                                // 如果有失败的卡密信息，添加到错误消息中
                                if (data.failed_kamis && data.failed_kamis.length > 0) {
                                    failedKamisHtml = '<div style="text-align: left; margin-top: 10px;"><strong>失败的卡密：</strong><pre style="max-height: 150px; overflow-y: auto; background: #f5f5f5; padding: 10px; border-radius: 5px; margin-top: 5px;">' + 
                                        data.failed_kamis.join('\n') + '</pre></div>';
                                }
                                
                                Swal.fire({
                                    title: '生成失败',
                                    html: errorMsg + failedKamisHtml,
                                    icon: 'error'
                                });
                            }
                        })
                        .catch(error => {
                            console.error('生成卡密失败:', error);
                            Swal.fire({
                                title: '生成失败',
                                text: '请求出错，请稍后再试',
                                icon: 'error'
                            });
                        });
                }
            });
        });

        // 删除选中的卡密
        document.getElementById('delete-selected-btn').addEventListener('click', function() {
            const checkStatus = layui.table.checkStatus('kami-table');
            const data = checkStatus.data;
            
            if (data.length === 0) {
                Swal.fire({
                    title: '请选择卡密',
                    text: '请先选择要删除的卡密',
                    icon: 'info'
                });
        return;
    }

            Swal.fire({
                title: '确认删除',
                text: `确定要删除选中的 ${data.length} 个卡密吗？`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: '确定删除',
                cancelButtonText: '取消'
            }).then((result) => {
                if (result.isConfirmed) {
                    const kamiList = data.map(item => item.kami).join(',');
                    
                    fetch(`api/delete_kami.php?kamiList=${encodeURIComponent(kamiList)}`)
        .then(response => response.json())
                        .then(data => {
                            if (data.code === "200") {
                                Swal.fire({
                                    title: '删除成功',
                                    text: data.msg,
                                    icon: 'success'
                                }).then(() => {
                                    refreshTable();
                                    loadStats();
                                });
            } else {
                                Swal.fire({
                                    title: '删除失败',
                                    text: data.error || '操作失败，请稍后再试',
                                    icon: 'error'
                                });
            }
        })
        .catch(error => {
                            console.error('删除卡密失败:', error);
                            Swal.fire({
                                title: '删除失败',
                                text: '请求出错，请稍后再试',
                                icon: 'error'
                            });
                        });
                }
            });
        });

        // 刷新表格按钮
        document.getElementById('refresh-table-btn').addEventListener('click', function() {
            // 重置搜索和筛选条件
            document.getElementById('kami-search').value = '';
            document.getElementById('kami-type-filter').value = '';
            document.getElementById('kami-status-filter').value = '';
            
            // 刷新表格
            refreshTable();
            loadStats();
        });

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            initTable();
            updateValidDays(); // 初始化有效天数
            
            // 添加搜索按钮点击事件
            document.getElementById('search-btn').addEventListener('click', function() {
                refreshTable();
            });
            
            // 添加回车键搜索功能
            document.getElementById('kami-search').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    refreshTable();
                }
            });
            
            // 添加类型和状态筛选变化事件
            document.getElementById('kami-type-filter').addEventListener('change', function() {
                refreshTable();
            });
            
            document.getElementById('kami-status-filter').addEventListener('change', function() {
                refreshTable();
            });
            
            // 加载导航链接设置
            loadLinkSettings();
            
            // 保存导航链接设置
            document.getElementById('save-links-btn').addEventListener('click', function() {
                saveLinkSettings();
            });
            
            setupIconSelectors();
        });
        
        // 加载导航链接设置
        function loadLinkSettings() {
            fetch('api/get_links.php?admin=1')
                .then(response => response.json())
                .then(data => {
                    if (data.code == 200) {
                        // 填充表单
                        Object.keys(data.links).forEach(linkName => {
                            const link = data.links[linkName];
                            
                            // 设置URL
                            const urlInput = document.getElementById(linkName + '_url');
                            if (urlInput) {
                                urlInput.value = link.url || '';
                            }
                            
                            // 设置显示名称
                            const displayNameInput = document.getElementById(linkName + '_display_name');
                            if (displayNameInput) {
                                displayNameInput.value = link.display_name || '';
                            }
                            
                            // 设置图标
                            const iconInput = document.getElementById(linkName + '_icon');
                            if (iconInput) {
                                iconInput.value = link.icon || 'fa-link';
                                updateIconPreview(iconInput);
                            }
                            
                            // 设置颜色
                            const colorPicker = document.getElementById(linkName + '_color');
                            if (colorPicker && link.color) {
                                colorPicker.value = link.color;
                                updateColorPreview(colorPicker);
                            }
                            
                            // 设置启用状态
                            const activeCheckbox = document.getElementById(linkName + '_active');
                            if (activeCheckbox) {
                                activeCheckbox.checked = link.is_active === 1;
                            }
                        });
                    } else {
                        console.error('加载链接设置失败:', data.error);
                        // 显示错误提示
                        Swal.fire({
                            title: '加载失败',
                            text: '无法加载链接设置，请稍后再试',
                            icon: 'error'
                        });
                    }
                })
                .catch(error => {
                    console.error('请求链接设置失败:', error);
                    // 显示错误提示
                    Swal.fire({
                        title: '加载失败',
                        text: '请求链接设置失败，请稍后再试',
                        icon: 'error'
                    });
                });
        }
        
        // 保存导航链接设置
        function saveLinkSettings() {
            // 收集所有链接的设置
            const formData = new FormData();
            
            // 主要链接和额外链接
            const linkNames = ['buy_account', 'faq', 'customer_service', 'extra_link_1', 'extra_link_2'];
            
            linkNames.forEach(linkName => {
                const urlInput = document.getElementById(linkName + '_url');
                const displayNameInput = document.getElementById(linkName + '_display_name');
                const iconInput = document.getElementById(linkName + '_icon');
                const activeCheckbox = document.getElementById(linkName + '_active');
                const colorPicker = document.getElementById(linkName + '_color');
                
                if (urlInput) formData.append(linkName + '_url', urlInput.value.trim());
                if (displayNameInput) formData.append(linkName + '_display_name', displayNameInput.value.trim());
                if (iconInput) formData.append(linkName + '_icon', iconInput.value.trim() || 'fa-link');
                if (activeCheckbox) formData.append(linkName + '_active', activeCheckbox.checked ? '1' : '0');
                if (colorPicker) formData.append(linkName + '_color', colorPicker.value);
            });
            
            // 验证URL格式 (简单验证)
            const urlPattern = /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([\/\w.-]*)*\/?$/;
            
            for (const linkName of linkNames) {
                const urlInput = document.getElementById(linkName + '_url');
                const activeCheckbox = document.getElementById(linkName + '_active');
                
                if (urlInput && activeCheckbox && activeCheckbox.checked && urlInput.value.trim() && !urlPattern.test(urlInput.value.trim())) {
                    Swal.fire({
                        title: '格式错误',
                        text: `${linkName.replace(/_/g, ' ')} 链接格式不正确`,
                        icon: 'error'
                    });
                    return;
                }
            }
            
            // 显示加载中提示
            Swal.fire({
                title: '保存中',
                text: '正在保存链接设置...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            // 发送请求保存设置
            fetch('api/save_links.php', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.code == 200) {
                        // 保存成功
                        Swal.fire({
                            title: '保存成功',
                            text: '链接设置已成功保存',
                            icon: 'success'
                        });
                    } else {
                        // 保存失败
                        Swal.fire({
                            title: '保存失败',
                            text: data.error || '保存链接设置失败，请稍后再试',
                            icon: 'error'
                        });
                    }
                })
                .catch(error => {
                    console.error('保存链接设置失败:', error);
                    // 显示错误提示
                    Swal.fire({
                        title: '保存失败',
                        text: '请求保存链接设置失败，请稍后再试',
                        icon: 'error'
                    });
                });
        }
        
        // 设置图标预览和选择功能
        function setupIconSelectors() {
            // 为所有图标输入框添加输入事件监听
            document.querySelectorAll('.icon-input').forEach(input => {
                // 初始化图标预览
                updateIconPreview(input);
                
                // 监听输入变化
                input.addEventListener('input', function() {
                    updateIconPreview(this);
                });
            });
            
            // 为所有图标选项添加点击事件
            document.querySelectorAll('.icon-option').forEach(option => {
                option.addEventListener('click', function() {
                    const iconName = this.getAttribute('data-icon');
                    const inputId = this.closest('.link-item').querySelector('.icon-input').id;
                    
                    // 设置输入框的值
                    document.getElementById(inputId).value = iconName;
                    
                    // 更新预览
                    updateIconPreview(document.getElementById(inputId));
                    
                    // 高亮选中的图标选项
                    this.closest('.icon-suggestions').querySelectorAll('.icon-option').forEach(opt => {
                        opt.style.backgroundColor = '#e0e5ec';
                        opt.style.color = '#44476a';
                    });
                    
                    this.style.backgroundColor = '#44476a';
                    this.style.color = 'white';
                });
            });
            
            // 设置颜色选择器事件监听
            document.querySelectorAll('.color-picker').forEach(colorPicker => {
                // 初始化颜色预览
                updateColorPreview(colorPicker);
                
                // 监听颜色变化
                colorPicker.addEventListener('input', function() {
                    updateColorPreview(this);
                });
            });
        }
        
        // 更新颜色预览
        function updateColorPreview(colorPicker) {
            const linkItem = colorPicker.closest('.link-item');
            const colorPreview = colorPicker.parentElement.querySelector('.color-preview');
            const iconInput = linkItem.querySelector('.icon-input');
            const iconName = iconInput ? iconInput.value.trim() : 'fa-link';
            const iconClass = iconName.startsWith('fa-') ? iconName : 'fa-' + iconName;
            
            // 更新预览区域的背景颜色
            colorPreview.style.backgroundColor = colorPicker.value;
            
            // 更新预览区域的图标
            const iconElement = colorPreview.querySelector('i');
            if (iconElement) {
                iconElement.className = 'fas ' + iconClass;
            }
        }
        
        // 更新图标预览
        function updateIconPreview(input) {
            const iconName = input.value.trim();
            const previewContainer = input.parentElement.querySelector('.icon-preview');
            
            if (previewContainer) {
                // 清空当前预览
                previewContainer.innerHTML = '';
                
                // 如果有图标名称，添加预览
                if (iconName) {
                    const iconClass = iconName.startsWith('fa-') ? iconName : 'fa-' + iconName;
                    const iconElement = document.createElement('i');
                    iconElement.className = 'fas ' + iconClass;
                    previewContainer.appendChild(iconElement);
                }
            }
            
            // 同步高亮当前选中的图标选项
            const suggestions = input.closest('.link-item').querySelector('.icon-suggestions');
            if (suggestions) {
                suggestions.querySelectorAll('.icon-option').forEach(opt => {
                    if (opt.getAttribute('data-icon') === iconName) {
                        opt.style.backgroundColor = '#44476a';
                        opt.style.color = 'white';
                    } else {
                        opt.style.backgroundColor = '#e0e5ec';
                        opt.style.color = '#44476a';
                    }
                });
            }
            
            // 更新颜色预览中的图标
            const linkItem = input.closest('.link-item');
            const colorPreview = linkItem.querySelector('.color-preview');
            if (colorPreview) {
                const iconElement = colorPreview.querySelector('i');
                if (iconElement) {
                    const iconClass = iconName.startsWith('fa-') ? iconName : 'fa-' + iconName;
                    iconElement.className = 'fas ' + iconClass;
                }
            }
        }
    </script>
</body>
</html>