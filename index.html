<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adobe账号查询</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.3.0/dist/sweetalert2.min.css">
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: #e0e5ec;
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 容器样式 */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            width: 100%;
        }

        /* 标题样式 */
        header {
            text-align: center;
            padding: 40px 20px;
            background-color: #e0e5ec;
            box-shadow: 8px 8px 15px rgba(163, 177, 198, 0.5),
                        -8px -8px 15px rgba(255, 255, 255, 0.6);
            margin-bottom: 30px;
            border-radius: 15px;
        }

        header h1 {
            font-size: 2.5rem;
            color: #4CAF50;
            margin-bottom: 10px;
        }

        header p {
            font-size: 1.2rem;
            color: #666;
        }

        /* 拟态表单样式 */
        .form-container {
            background-color: #e0e5ec;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 8px 8px 15px rgba(163, 177, 198, 0.5),
                        -8px -8px 15px rgba(255, 255, 255, 0.6);
        }

        .form-title {
            text-align: center;
            margin-bottom: 25px;
            color: #4CAF50;
            font-size: 1.8rem;
        }

        .form-title i {
            margin-right: 10px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        input[type="text"],
        input[type="password"],
        input[type="email"],
        select {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 50px;
            background-color: #e0e5ec;
            color: #333;
            font-size: 1rem;
            box-shadow: inset 5px 5px 10px rgba(163, 177, 198, 0.5),
                        inset -5px -5px 10px rgba(255, 255, 255, 0.6);
            transition: all 0.3s ease;
        }

        input:focus,
        select:focus {
            outline: none;
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.5),
                        inset -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        /* 按钮样式 */
        .btn {
            display: block;
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 50px;
            background-color: #4CAF50;
            color: white;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.5),
                        -5px -5px 10px rgba(255, 255, 255, 0.6);
            transition: all 0.3s ease;
            text-align: center;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 7px 7px 15px rgba(163, 177, 198, 0.5),
                        -7px -7px 15px rgba(255, 255, 255, 0.6);
        }

        .btn:active {
            transform: translateY(0);
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5),
                        -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        .btn i {
            margin-right: 8px;
        }

        /* 页脚样式 */
        footer {
            text-align: center;
            padding: 20px;
            margin-top: auto;
            color: #666;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .form-container {
                padding: 20px;
            }

            header {
                padding: 30px 15px;
            }

            header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Adobe账号查询系统</h1>
            <p>快速查询您的账号信息</p>
        </header>

        <div class="form-container">
            <h2 class="form-title"><i class="fas fa-key"></i>账号信息查询</h2>
            <form id="queryKamiForm">
                <div class="form-group">
                    <label for="queryKamiInput">卡密账号</label>
                    <input type="text" id="queryKamiInput" name="queryKamiInput" placeholder="请输入您购买的账号 例如：<EMAIL>" required onkeydown="return event.key !== ' '" oninput="this.value = this.value.replace(/\s/g, '')">
                </div>
                <button type="submit" class="btn">
                    <i class="fas fa-search"></i>
                    <span>点击查询</span>
                </button>
            </form>
        </div>
    </div>

    <footer>
        <p>&copy; 2025 卡密查询系统. 保留所有权利.</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.3.0/dist/sweetalert2.min.js"></script>
    <script>
    // 查询卡密信息
    document.getElementById('queryKamiForm').addEventListener('submit', function(event) {
        event.preventDefault(); // 阻止表单默认提交行为

        const queryKamiInput = document.getElementById('queryKamiInput').value;
        
        if (!queryKamiInput.trim()) {
            // 如果输入为空，显示错误提示
            Swal.fire({
                title: '输入错误',
                text: '请输入要查询的卡密',
                icon: 'error'
            });
            return;
        }

        // 显示加载中提示
        Swal.fire({
            title: '查询中',
            text: '正在查询卡密信息，请稍候...',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // 添加时间戳参数以避免缓存
        const timestamp = new Date().getTime();
        
        // 发起请求查询卡密信息
        fetch(`api/kmcx.php?kami=${encodeURIComponent(queryKamiInput)}&_t=${timestamp}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('请求失败');
                }
                return response.json();
            })
            .then(data => {
                // 关闭加载提示
                Swal.close();
                
                if (data.code == 200) {
                    // 查询成功，重定向到结果页面，不进行URL编码
                    window.location.href = `/${queryKamiInput}`;
                } else {
                    // 查询失败
                    Swal.fire({
                        title: '查询失败',
                        text: data.msg || '未找到卡密信息！',
                        icon: 'error'
                    });
                }
            })
            .catch(error => {
                // 显示错误信息
                Swal.fire({
                    title: '查询失败',
                    text: error.message,
                    icon: 'error'
                });
            });
    });
    </script>
</body>
</html>