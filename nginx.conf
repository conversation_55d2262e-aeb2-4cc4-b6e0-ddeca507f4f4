server {
    listen 80;
    server_name xiao.adobe.so.kg;
    root /path/to/your/website;  # 请替换为您网站的实际路径
    index index.html index.php;

    # 设置字符集
    charset utf-8;

    # 处理API请求
    location /api/ {
        try_files $uri $uri/ /api/$uri =404;
    }

    # 处理带有特殊字符的URL
    location ~ ^/([^/]+)$ {
        try_files $uri $uri/ /result.html;
    }

    # 设置CORS头
    add_header Access-Control-Allow-Origin "*";
    
    # 设置缓存控制
    location ~* \.(css|js)$ {
        expires 7d;
    }
    
    location ~* \.(jpg|jpeg|png|gif|ico|svg)$ {
        expires 30d;
    }
    
    location ~* \.(html|htm)$ {
        expires 1h;
    }

    # 处理PHP文件
    location ~ \.php$ {
        fastcgi_pass unix:/run/php/php7.4-fpm.sock;  # 请根据您的PHP版本调整
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
    }
} 