2025-06-09 13:48:55 - 卡密生成请求开始
2025-06-09 13:48:55 - 会话ID: qtiq7hbeur487uf0lkdp9qic9m
2025-06-09 13:48:55 - 会话状态: 活动
2025-06-09 13:48:55 - 会话数据: A<PERSON>y
(
    [username] => admin
)

2025-06-09 13:48:55 - 请求参数: count=10, type=1
2025-06-09 13:48:55 - 执行SQL: SHOW TABLES LIKE 'kami'
2025-06-09 13:48:55 - 准备插入SQL: INSERT INTO kami (kami, type, zt, vip_time, qq, user) VALUES (?, ?, 0, '', '', ?)
2025-06-09 13:48:55 - 当前用户: admin
2025-06-09 13:48:55 - 尝试生成卡密: 0YQ0ch1EWp (尝试 1/5)
2025-06-09 13:48:55 - 检查卡密是否存在: 0YQ0ch1EWp
2025-06-09 13:48:55 - 卡密 0YQ0ch1EWp 不存在
2025-06-09 13:48:55 - 卡密插入成功: 0YQ0ch1EWp
2025-06-09 13:48:55 - 尝试生成卡密: oIrsouNP7j (尝试 1/5)
2025-06-09 13:48:55 - 检查卡密是否存在: oIrsouNP7j
2025-06-09 13:48:55 - 卡密 oIrsouNP7j 不存在
2025-06-09 13:48:55 - 卡密插入成功: oIrsouNP7j
2025-06-09 13:48:55 - 尝试生成卡密: dmxVZhcybG (尝试 1/5)
2025-06-09 13:48:55 - 检查卡密是否存在: dmxVZhcybG
2025-06-09 13:48:55 - 卡密 dmxVZhcybG 不存在
2025-06-09 13:48:55 - 卡密插入成功: dmxVZhcybG
2025-06-09 13:48:55 - 尝试生成卡密: ig9l8BTvzV (尝试 1/5)
2025-06-09 13:48:55 - 检查卡密是否存在: ig9l8BTvzV
2025-06-09 13:48:55 - 卡密 ig9l8BTvzV 不存在
2025-06-09 13:48:55 - 卡密插入成功: ig9l8BTvzV
2025-06-09 13:48:55 - 尝试生成卡密: zDqWvp9m0m (尝试 1/5)
2025-06-09 13:48:55 - 检查卡密是否存在: zDqWvp9m0m
2025-06-09 13:48:55 - 卡密 zDqWvp9m0m 不存在
2025-06-09 13:48:55 - 卡密插入成功: zDqWvp9m0m
2025-06-09 13:48:55 - 尝试生成卡密: Oz1ueRGT4y (尝试 1/5)
2025-06-09 13:48:55 - 检查卡密是否存在: Oz1ueRGT4y
2025-06-09 13:48:55 - 卡密 Oz1ueRGT4y 不存在
2025-06-09 13:48:55 - 卡密插入成功: Oz1ueRGT4y
2025-06-09 13:48:55 - 尝试生成卡密: g967wPJLZG (尝试 1/5)
2025-06-09 13:48:55 - 检查卡密是否存在: g967wPJLZG
2025-06-09 13:48:55 - 卡密 g967wPJLZG 不存在
2025-06-09 13:48:55 - 卡密插入成功: g967wPJLZG
2025-06-09 13:48:55 - 尝试生成卡密: gMdKOHikTW (尝试 1/5)
2025-06-09 13:48:55 - 检查卡密是否存在: gMdKOHikTW
2025-06-09 13:48:55 - 卡密 gMdKOHikTW 不存在
2025-06-09 13:48:55 - 卡密插入成功: gMdKOHikTW
2025-06-09 13:48:55 - 尝试生成卡密: wZZoSjfwCE (尝试 1/5)
2025-06-09 13:48:55 - 检查卡密是否存在: wZZoSjfwCE
2025-06-09 13:48:55 - 卡密 wZZoSjfwCE 不存在
2025-06-09 13:48:55 - 卡密插入成功: wZZoSjfwCE
2025-06-09 13:48:55 - 尝试生成卡密: hlCKdb9vz6 (尝试 1/5)
2025-06-09 13:48:55 - 检查卡密是否存在: hlCKdb9vz6
2025-06-09 13:48:55 - 卡密 hlCKdb9vz6 不存在
2025-06-09 13:48:55 - 卡密插入成功: hlCKdb9vz6
2025-06-09 13:48:55 - 成功生成 10 个卡密
2025-06-09 13:48:55 - 数据库连接已关闭
2025-06-26 09:24:30 - 卡密生成请求开始
2025-06-26 09:24:30 - 会话ID: givtd4f9rt8ffo789q0f10h4og
2025-06-26 09:24:30 - 会话状态: 活动
2025-06-26 09:24:30 - 会话数据: Array
(
    [username] => haodahao
)

2025-06-26 09:24:30 - 请求参数: type=Adobe-全家桶授权-14天, valid_days=14, device_limit=1
2025-06-26 09:24:30 - 执行SQL: SHOW TABLES LIKE 'kami'
2025-06-26 09:24:30 - 准备插入SQL: INSERT INTO kami (kami, type, valid_days, device_limit, password, remark) VALUES (?, ?, ?, ?, ?, ?)
2025-06-26 09:24:30 - 卡密数量: 1
2025-06-26 09:24:30 - 当前用户: haodahao
2025-06-26 09:24:30 - 处理卡密: 1212
2025-06-26 09:24:30 - 检查卡密是否存在: 1212
2025-06-26 09:24:30 - 卡密 1212 不存在
2025-06-26 09:24:30 - 卡密插入成功: 1212
2025-06-26 09:24:30 - 成功生成 1 个卡密
2025-06-26 09:24:30 - 数据库连接已关闭
2025-07-01 09:58:05 - 卡密生成请求开始
2025-07-01 09:58:05 - 会话ID: s3irq91u77ksjc005rbbha0ddc
2025-07-01 09:58:05 - 会话状态: 活动
2025-07-01 09:58:05 - 会话数据: Array
(
    [username] => haodahao
)

2025-07-01 09:58:05 - 请求参数: type=Adobe-全家桶授权-14天, valid_days=14, device_limit=1
2025-07-01 09:58:05 - 执行SQL: SHOW TABLES LIKE 'kami'
2025-07-01 09:58:05 - 准备插入SQL: INSERT INTO kami (kami, type, valid_days, device_limit, password, remark) VALUES (?, ?, ?, ?, ?, ?)
2025-07-01 09:58:05 - 卡密数量: 1
2025-07-01 09:58:05 - 当前用户: haodahao
2025-07-01 09:58:05 - 处理卡密: <EMAIL>
2025-07-01 09:58:05 - 检查卡密是否存在: <EMAIL>
2025-07-01 09:58:05 - 卡密 <EMAIL> 不存在
2025-07-01 09:58:05 - 卡密插入成功: <EMAIL>
2025-07-01 09:58:05 - 成功生成 1 个卡密
2025-07-01 09:58:05 - 数据库连接已关闭
2025-07-10 10:39:57 - 卡密生成请求开始
2025-07-10 10:39:57 - 会话ID: 5rbkrak24lfqu6lu36sk228s63
2025-07-10 10:39:57 - 会话状态: 活动
2025-07-10 10:39:57 - 会话数据: Array
(
    [username] => haodahao
)

2025-07-10 10:39:57 - 请求参数: type=Adobe-全家桶授权-14天, valid_days=14, device_limit=1
2025-07-10 10:39:57 - 执行SQL: SHOW TABLES LIKE 'kami'
2025-07-10 10:39:57 - 准备插入SQL: INSERT INTO kami (kami, type, valid_days, device_limit, password, remark) VALUES (?, ?, ?, ?, ?, ?)
2025-07-10 10:39:57 - 卡密数量: 4
2025-07-10 10:39:57 - 当前用户: haodahao
2025-07-10 10:39:57 - 处理卡密: <EMAIL>
2025-07-10 10:39:57 - 检查卡密是否存在: <EMAIL>
2025-07-10 10:39:57 - 卡密 <EMAIL> 不存在
2025-07-10 10:39:57 - 卡密插入成功: <EMAIL>
2025-07-10 10:39:57 - 处理卡密: <EMAIL>
2025-07-10 10:39:57 - 检查卡密是否存在: <EMAIL>
2025-07-10 10:39:57 - 卡密 <EMAIL> 不存在
2025-07-10 10:39:57 - 卡密插入成功: <EMAIL>
2025-07-10 10:39:57 - 处理卡密: <EMAIL>
2025-07-10 10:39:57 - 检查卡密是否存在: <EMAIL>
2025-07-10 10:39:57 - 卡密 <EMAIL> 不存在
2025-07-10 10:39:57 - 卡密插入成功: <EMAIL>
2025-07-10 10:39:57 - 处理卡密: <EMAIL>
2025-07-10 10:39:57 - 检查卡密是否存在: <EMAIL>
2025-07-10 10:39:57 - 卡密 <EMAIL> 不存在
2025-07-10 10:39:57 - 卡密插入成功: <EMAIL>
2025-07-10 10:39:57 - 成功生成 4 个卡密
2025-07-10 10:39:57 - 数据库连接已关闭
2025-07-17 10:48:28 - 卡密生成请求开始
2025-07-17 10:48:28 - 会话ID: j2bf6tj00uplismj92u7sgi84i
2025-07-17 10:48:28 - 会话状态: 活动
2025-07-17 10:48:28 - 会话数据: Array
(
    [username] => haodahao
)

2025-07-17 10:48:28 - 请求参数: type=Adobe-全家桶授权-14天, valid_days=14, device_limit=1
2025-07-17 10:48:28 - 执行SQL: SHOW TABLES LIKE 'kami'
2025-07-17 10:48:28 - 准备插入SQL: INSERT INTO kami (kami, type, valid_days, device_limit, password, remark) VALUES (?, ?, ?, ?, ?, ?)
2025-07-17 10:48:28 - 卡密数量: 1
2025-07-17 10:48:28 - 当前用户: haodahao
2025-07-17 10:48:28 - 处理卡密: <EMAIL>
2025-07-17 10:48:28 - 检查卡密是否存在: <EMAIL>
2025-07-17 10:48:28 - 卡密 <EMAIL> 不存在
2025-07-17 10:48:28 - 卡密插入成功: <EMAIL>
2025-07-17 10:48:28 - 成功生成 1 个卡密
2025-07-17 10:48:28 - 数据库连接已关闭
