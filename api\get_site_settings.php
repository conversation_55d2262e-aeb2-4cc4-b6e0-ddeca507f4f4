<?php
session_start();
header('Content-Type: application/json');
require_once 'config.php';

if (!isset($_SESSION['username'])) {
    echo json_encode(['code' => 401, 'message' => '未授权访问']);
    exit;
}

$settings = [];
$query = "SELECT * FROM sit_setting";
$result = $conn->query($query);

if ($result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
}

echo json_encode([
    'code' => 200,
    'data' => [
        'title' => $settings['title'] ?? '默认标题',
        'announcement' => $settings['announcement'] ?? ''
    ]
]);

$conn->close();