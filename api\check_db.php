<?php
// 关闭直接显示错误，但记录它们
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', 'error.log');

// 设置内容类型为JSON
header('Content-Type: application/json');

// 会话配置
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // 如果使用HTTPS，设置为1

// 启动会话
session_start();

// 检查登录状态
if (!isset($_SESSION['username'])) {
    echo json_encode(array("code" => "401", "error" => "未授权访问"));
    exit();
}

// 数据库配置
include 'config.php';

// 检查数据库连接
if ($conn->connect_error) {
    echo json_encode(array("code" => "500", "error" => "数据库连接失败: " . $conn->connect_error));
    exit();
}

try {
    // 获取数据库版本信息
    $versionResult = $conn->query("SELECT VERSION() as version");
    $versionRow = $versionResult->fetch_assoc();
    $dbVersion = $versionRow['version'];
    
    // 获取数据库字符集信息
    $charsetResult = $conn->query("SHOW VARIABLES LIKE 'character_set_database'");
    $charsetRow = $charsetResult->fetch_assoc();
    $dbCharset = $charsetRow['Value'];
    
    // 获取数据库排序规则信息
    $collationResult = $conn->query("SHOW VARIABLES LIKE 'collation_database'");
    $collationRow = $collationResult->fetch_assoc();
    $dbCollation = $collationRow['Value'];
    
    // 检查kami表结构
    $tableInfoResult = $conn->query("SHOW CREATE TABLE kami");
    $tableInfoRow = $tableInfoResult->fetch_assoc();
    $tableCreateSql = $tableInfoRow['Create Table'];
    
    // 获取kami表的字段信息
    $columnsResult = $conn->query("SHOW COLUMNS FROM kami");
    $columns = [];
    while ($row = $columnsResult->fetch_assoc()) {
        $columns[] = $row;
    }
    
    // 测试插入一个包含特殊字符的卡密
    $testKami = "test_kami_" . time() . "@example.com";
    $testType = "测试类型";
    $testValidDays = 30;
    $testDeviceLimit = 1;
    $testPassword = "test123";
    $testRemark = "测试备注";
    
    $insertSql = "INSERT INTO kami (kami, type, valid_days, device_limit, password, remark) VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($insertSql);
    
    if (!$stmt) {
        throw new Exception("预处理测试插入语句失败: " . $conn->error);
    }
    
    $stmt->bind_param("ssiiss", $testKami, $testType, $testValidDays, $testDeviceLimit, $testPassword, $testRemark);
    $insertSuccess = $stmt->execute();
    $insertError = $stmt->error;
    $stmt->close();
    
    // 如果插入成功，删除测试卡密
    if ($insertSuccess) {
        $conn->query("DELETE FROM kami WHERE kami = '$testKami'");
    }
    
    // 返回检查结果
    echo json_encode(array(
        "code" => "200",
        "msg" => "数据库检查完成",
        "db_info" => array(
            "version" => $dbVersion,
            "charset" => $dbCharset,
            "collation" => $dbCollation
        ),
        "table_create_sql" => $tableCreateSql,
        "columns" => $columns,
        "test_insert" => array(
            "success" => $insertSuccess,
            "error" => $insertError,
            "test_kami" => $testKami
        )
    ), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode(array("code" => "500", "error" => $e->getMessage()));
} finally {
    if (isset($conn) && $conn) {
        $conn->close();
    }
}
?> 