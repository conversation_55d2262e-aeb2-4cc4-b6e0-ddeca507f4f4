<?php
// 关闭直接显示错误，但记录它们
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'error.log');

// 设置内容类型为JSON
header('Content-Type: application/json');

// 记录请求信息
file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 卡密生成请求开始\n", FILE_APPEND);

// 会话配置
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // 如果使用HTTPS，设置为1

// 启动会话
session_start();

// 调试会话信息
file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 会话ID: " . session_id() . "\n", FILE_APPEND);
file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 会话状态: " . (session_status() == PHP_SESSION_ACTIVE ? "活动" : "非活动") . "\n", FILE_APPEND);
file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 会话数据: " . print_r($_SESSION, true) . "\n", FILE_APPEND);

if (!isset($_SESSION['username'])) {
    file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 未授权访问\n", FILE_APPEND);
    echo json_encode(array("code" => "401", "error" => "未授权访问"));
    exit();
}

// 数据库配置
include 'config.php';

// 记录数据库连接状态
if ($conn->connect_error) {
    file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 数据库连接失败: " . $conn->connect_error . "\n", FILE_APPEND);
    echo json_encode(array("code" => "500", "error" => "数据库连接失败"));
    exit();
}

// 接收POST请求参数
$type = isset($_POST['type']) ? $conn->real_escape_string($_POST['type']) : null;
$valid_days = isset($_POST['valid_days']) ? intval($_POST['valid_days']) : 0;
$device_limit = isset($_POST['device_limit']) ? intval($_POST['device_limit']) : 1;
$password = isset($_POST['password']) ? $conn->real_escape_string($_POST['password']) : '';
$remark = isset($_POST['remark']) ? $conn->real_escape_string($_POST['remark']) : '';
$kami_content = isset($_POST['kami_content']) ? trim($_POST['kami_content']) : '';

file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 请求参数: type=$type, valid_days=$valid_days, device_limit=$device_limit\n", FILE_APPEND);

if (!$type || $valid_days <= 0 || !$kami_content) {
    file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 参数不全或无效\n", FILE_APPEND);
    echo json_encode(array("code" => "403", "error" => "参数不全或无效"));
    exit();
}

// 检查数据库中是否已经存在相同的卡密
function isKamiExists($conn, $kami) {
    $checkSql = "SELECT COUNT(*) FROM kami WHERE kami = ?";
    file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 检查卡密是否存在: $kami\n", FILE_APPEND);
    
    $stmt = $conn->prepare($checkSql);
    if (!$stmt) {
        file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 预处理检查卡密语句失败: " . $conn->error . "\n", FILE_APPEND);
        return true; // 如果预处理语句失败，返回true以避免插入
    }
    
    $stmt->bind_param("s", $kami);
    if (!$stmt->execute()) {
        file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 执行检查卡密语句失败: " . $stmt->error . "\n", FILE_APPEND);
        $stmt->close();
        return true;
    }
    
    $stmt->bind_result($count);
    $stmt->fetch();
    $exists = $count > 0;
    
    file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 卡密 $kami " . ($exists ? "已存在" : "不存在") . "\n", FILE_APPEND);
    
    $stmt->close();
    return $exists;
}

try {
    // 检查kami表是否存在
    $tableCheckSql = "SHOW TABLES LIKE 'kami'";
    file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 执行SQL: $tableCheckSql\n", FILE_APPEND);
    $tableResult = $conn->query($tableCheckSql);
    
    if (!$tableResult) {
        file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 查询表失败: " . $conn->error . "\n", FILE_APPEND);
        throw new Exception("查询表失败: " . $conn->error);
    }
    
    // 分割卡密内容为数组
    $kamiContent = str_replace("\r\n", "\n", $kami_content); // 统一换行符
    $kamiArray = explode("\n", $kamiContent);
    $kamiArray = array_map('trim', $kamiArray);
    $kamiArray = array_filter($kamiArray); // 移除空行
    
    // 生成并插入卡密
    $successCount = 0;
    $failedKamis = [];
    $insertSql = "INSERT INTO kami (kami, type, valid_days, device_limit, password, remark) VALUES (?, ?, ?, ?, ?, ?)";
    file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 准备插入SQL: $insertSql\n", FILE_APPEND);
    file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 卡密数量: " . count($kamiArray) . "\n", FILE_APPEND);
    
    $stmt = $conn->prepare($insertSql);

    if (!$stmt) {
        file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 预处理语句失败: " . $conn->error . "\n", FILE_APPEND);
        throw new Exception("预处理语句失败: " . $conn->error);
    }

    // 获取当前用户名
    $username = $_SESSION['username'];
    file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 当前用户: $username\n", FILE_APPEND);

    foreach ($kamiArray as $kami) {
        if (empty($kami)) continue;
        
        // 清理卡密字符串，移除可能导致问题的字符
        $kami = trim($kami);
        
        // 记录卡密内容
        file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 处理卡密: " . htmlspecialchars($kami) . "\n", FILE_APPEND);
        
        try {
            // 检查卡密是否已存在
            if (!isKamiExists($conn, $kami)) {
                $stmt->bind_param("ssiiss", $kami, $type, $valid_days, $device_limit, $password, $remark);
                if ($stmt->execute()) {
                    $successCount++;
                    file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 卡密插入成功: " . htmlspecialchars($kami) . "\n", FILE_APPEND);
                } else {
                    $failedKamis[] = $kami;
                    file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 卡密插入失败: " . $stmt->error . " - 卡密内容: " . htmlspecialchars($kami) . "\n", FILE_APPEND);
                }
            } else {
                $failedKamis[] = $kami;
                file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 卡密已存在: " . htmlspecialchars($kami) . "\n", FILE_APPEND);
            }
        } catch (Exception $e) {
            $failedKamis[] = $kami;
            file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 处理卡密异常: " . $e->getMessage() . " - 卡密内容: " . htmlspecialchars($kami) . "\n", FILE_APPEND);
        }
    }

    if ($successCount > 0) {
        file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 成功生成 $successCount 个卡密\n", FILE_APPEND);
        
        // 如果有失败的卡密，返回部分成功的信息
        if (!empty($failedKamis)) {
            $failedCount = count($failedKamis);
            $response = array(
                "code" => "206", // 部分成功
                "msg" => "成功生成 $successCount 个卡密，$failedCount 个卡密生成失败",
                "count" => $successCount,
                "failed_count" => $failedCount
            );
            
            // 如果失败数量不多，可以返回具体的失败卡密
            if ($failedCount <= 5) {
                $response["failed_kamis"] = $failedKamis;
            }
            
            echo json_encode($response, JSON_UNESCAPED_UNICODE);
        } else {
            // 全部成功
            echo json_encode(array(
                "code" => "200", 
                "msg" => "卡密批量生成并添加成功", 
                "count" => $successCount
            ));
        }
    } else {
        file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 生成卡密失败，没有成功插入任何卡密\n", FILE_APPEND);
        
        // 如果有具体的失败卡密，返回更详细的错误信息
        if (!empty($failedKamis)) {
            $failedCount = count($failedKamis);
            $response = array(
                "code" => "404",
                "error" => "生成卡密失败，可能是卡密包含特殊字符或已存在",
                "failed_count" => $failedCount
            );
            
            // 如果失败数量不多，可以返回具体的失败卡密
            if ($failedCount <= 5) {
                $response["failed_kamis"] = $failedKamis;
            }
            
            echo json_encode($response, JSON_UNESCAPED_UNICODE);
        } else {
            throw new Exception("生成卡密失败，可能是卡密已存在或输入无效");
        }
    }

    $stmt->close();
} catch (Exception $e) {
    file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 异常: " . $e->getMessage() . "\n", FILE_APPEND);
    echo json_encode(array("code" => "404", "error" => $e->getMessage()));
} finally {
    if (isset($conn) && $conn) {
        $conn->close();
        file_put_contents('debug.log', date('Y-m-d H:i:s') . " - 数据库连接已关闭\n", FILE_APPEND);
    }
}
?>