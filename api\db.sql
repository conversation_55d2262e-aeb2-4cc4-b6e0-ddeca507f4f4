-- 卡密管理系统数据库结构

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

-- 管理员表
DROP TABLE IF EXISTS `admin`;
CREATE TABLE `admin` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(32) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 初始管理员账户，用户名: admin，密码: 123456
INSERT INTO `admin` (`id`, `username`, `password`) VALUES
(1, 'admin', 'e10adc3949ba59abbe56e057f20f883e');

-- 卡密表
DROP TABLE IF EXISTS `kami`;
CREATE TABLE `kami` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL COMMENT '卡密类型',
  `kami` varchar(255) NOT NULL COMMENT '卡密码',
  `valid_days` int(11) NOT NULL DEFAULT '0' COMMENT '有效天数',
  `device_limit` tinyint(1) NOT NULL DEFAULT '1' COMMENT '设备限制数',
  `password` varchar(255) DEFAULT '' COMMENT '卡密密码',
  `new_account` varchar(255) DEFAULT NULL COMMENT '新账号',
  `new_password` varchar(255) DEFAULT NULL COMMENT '新密码',
  `remark` text COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `use_time` datetime DEFAULT NULL COMMENT '使用时间',
  `expiry_time` datetime DEFAULT NULL COMMENT '到期时间',
  `zt` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态(0未使用,1已使用)',
  `user` varchar(50) DEFAULT 'admin' COMMENT '创建用户',
  PRIMARY KEY (`id`),
  UNIQUE KEY `kami` (`kami`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 2025-05-20 04:16:40