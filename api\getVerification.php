<?php
// 设置响应头
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Pragma: no-cache');
header('Expires: 0');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');

// 获取邮箱参数
$email = isset($_GET['email']) ? trim($_GET['email']) : '';

// 验证参数
if (empty($email)) {
    echo json_encode([
        'code' => 400,
        'message' => '缺少必要参数',
        'data' => null
    ]);
    exit;
}

// 记录访问日志（如果需要）
$ip = getClientIp();
logAccess('get_verification', "查询验证码: {$email}, IP: {$ip}");

// API信息
$apiUrl = "https://apimail.552500.xyz/admin/mails";
$apiKey = "hao123456";

// 构建API请求
$requestUrl = $apiUrl . "?limit=20&offset=0&email=" . urlencode($email);

// 发起API请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $requestUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json',
    'x-admin-auth: ' . $apiKey
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 5); // 减少超时时间
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false); // 禁止自动重定向

// 执行请求
$response = curl_exec($ch);
$error = curl_error($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

// 处理响应
if ($error) {
    echo json_encode([
        'code' => 500,
        'message' => '请求失败: ' . $error,
        'data' => null
    ]);
    exit;
}

// 返回API响应
echo $response;
exit;

// 获取客户端IP地址
function getClientIp() {
    if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $clientIp = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {
        $clientIp = $_SERVER['HTTP_CLIENT_IP'];
    } else {
        $clientIp = $_SERVER['REMOTE_ADDR'];
    }
    return $clientIp;
}

// 记录访问日志
function logAccess($action, $detail = '') {
    // 这里可以根据需要实现日志记录功能
    // 例如写入文件或数据库
    $logMessage = date('Y-m-d H:i:s') . " | {$action} | {$detail}\n";
    // 如果需要，可以将日志写入文件
    // file_put_contents('access.log', $logMessage, FILE_APPEND);
} 