<?php
session_start();
header('Content-Type: application/json');
require_once 'config.php';

if (!isset($_SESSION['username'])) {
    echo json_encode(['code' => 401, 'message' => '未授权访问']);
    exit;
}

$title = $_POST['title'] ?? '';
$announcement = $_POST['announcement'] ?? '';

$conn->begin_transaction();

try {
    // 更新标题
    $stmt = $conn->prepare("REPLACE INTO sit_setting (setting_key, setting_value) VALUES ('title', ?)");
    $stmt->bind_param("s", $title);
    $stmt->execute();
    
    // 更新公告
    $stmt = $conn->prepare("REPLACE INTO sit_setting (setting_key, setting_value) VALUES ('announcement', ?)");
    $stmt->bind_param("s", $announcement);
    $stmt->execute();

    $conn->commit();
    echo json_encode(['code' => 200, 'message' => '设置保存成功']);
} catch (Exception $e) {
    $conn->rollback();
    echo json_encode(['code' => 500, 'message' => '数据库错误: ' . $e->getMessage()]);
}

$stmt->close();
$conn->close();