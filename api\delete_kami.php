<?php
// 关闭直接显示错误，但记录它们
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'error.log');

// 设置内容类型为JSON
header('Content-Type: application/json');

// 会话配置
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // 如果使用HTTPS，设置为1

// 启动会话
session_start();

// 检查登录状态
if (!isset($_SESSION['username'])) {
    echo json_encode(array("code" => "401", "error" => "未授权访问"));
    exit();
}

// 数据库配置
include 'config.php';

// 检查数据库连接
if ($conn->connect_error) {
    echo json_encode(array("code" => "500", "error" => "数据库连接失败"));
    exit();
}

// 获取要删除的卡密列表
$kamiList = isset($_GET['kamiList']) ? $_GET['kamiList'] : '';

if (empty($kamiList)) {
    echo json_encode(array("code" => "403", "error" => "参数不全或无效"));
    exit();
}

try {
    // 分割卡密列表
    $kamiArray = explode(',', $kamiList);
    $successCount = 0;
    $failedKamis = [];
    
    // 准备删除语句
    $deleteSql = "DELETE FROM kami WHERE kami = ?";
    $stmt = $conn->prepare($deleteSql);
    
    if (!$stmt) {
        throw new Exception("预处理语句失败: " . $conn->error);
    }
    
    foreach ($kamiArray as $kami) {
        $kami = trim($kami);
        if (empty($kami)) continue;
        
        $stmt->bind_param("s", $kami);
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                $successCount++;
            } else {
                $failedKamis[] = $kami;
            }
        } else {
            $failedKamis[] = $kami;
        }
    }
    
    $stmt->close();
    
    if ($successCount > 0) {
        if (empty($failedKamis)) {
            echo json_encode(array(
                "code" => "200", 
                "msg" => "成功删除 $successCount 个卡密", 
                "count" => $successCount
            ));
        } else {
            $failedCount = count($failedKamis);
            echo json_encode(array(
                "code" => "206", 
                "msg" => "成功删除 $successCount 个卡密，$failedCount 个卡密删除失败", 
                "count" => $successCount,
                "failed_count" => $failedCount,
                "failed_kamis" => $failedKamis
            ));
        }
    } else {
        echo json_encode(array("code" => "404", "error" => "删除失败，未找到指定的卡密"));
    }
} catch (Exception $e) {
    echo json_encode(array("code" => "500", "error" => $e->getMessage()));
} finally {
    if (isset($conn) && $conn) {
        $conn->close();
    }
}
?>