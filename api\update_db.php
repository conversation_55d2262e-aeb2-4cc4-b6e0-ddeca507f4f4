<?php
// 关闭直接显示错误，但记录它们
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'error.log');

// 设置内容类型为JSON
header('Content-Type: application/json');

// 数据库配置
include 'config.php';

// 检查数据库连接
if ($conn->connect_error) {
    echo json_encode(array("code" => "500", "error" => "数据库连接失败: " . $conn->connect_error));
    exit();
}

// 记录日志函数
function log_message($message) {
    file_put_contents('update_db.log', date('Y-m-d H:i:s') . " - " . $message . "\n", FILE_APPEND);
}

// 开始记录
log_message("开始数据库结构更新");

// 检查字段是否存在的函数
function column_exists($conn, $table, $column) {
    $query = "SHOW COLUMNS FROM `$table` LIKE '$column'";
    $result = $conn->query($query);
    return $result->num_rows > 0;
}

// 更新结果数组
$updates = array();

// 添加新账号字段
if (!column_exists($conn, 'kami', 'new_account')) {
    log_message("添加 new_account 字段");
    $sql = "ALTER TABLE `kami` ADD COLUMN `new_account` VARCHAR(255) DEFAULT NULL AFTER `password`";
    if ($conn->query($sql) === TRUE) {
        $updates[] = "成功添加 new_account 字段";
        log_message("成功添加 new_account 字段");
    } else {
        $updates[] = "添加 new_account 字段失败: " . $conn->error;
        log_message("添加 new_account 字段失败: " . $conn->error);
    }
}

// 添加新密码字段
if (!column_exists($conn, 'kami', 'new_password')) {
    log_message("添加 new_password 字段");
    $sql = "ALTER TABLE `kami` ADD COLUMN `new_password` VARCHAR(255) DEFAULT NULL AFTER `new_account`";
    if ($conn->query($sql) === TRUE) {
        $updates[] = "成功添加 new_password 字段";
        log_message("成功添加 new_password 字段");
    } else {
        $updates[] = "添加 new_password 字段失败: " . $conn->error;
        log_message("添加 new_password 字段失败: " . $conn->error);
    }
}

// 检查到期时间字段
if (!column_exists($conn, 'kami', 'expiry_time')) {
    log_message("添加 expiry_time 字段");
    $sql = "ALTER TABLE `kami` ADD COLUMN `expiry_time` DATETIME DEFAULT NULL AFTER `use_time`";
    if ($conn->query($sql) === TRUE) {
        $updates[] = "成功添加 expiry_time 字段";
        log_message("成功添加 expiry_time 字段");
    } else {
        $updates[] = "添加 expiry_time 字段失败: " . $conn->error;
        log_message("添加 expiry_time 字段失败: " . $conn->error);
    }
}

// 关闭数据库连接
$conn->close();

// 返回更新结果
if (count($updates) > 0) {
    echo json_encode(array(
        "code" => "200",
        "msg" => "数据库结构更新完成",
        "updates" => $updates
    ));
} else {
    echo json_encode(array(
        "code" => "200",
        "msg" => "数据库结构已是最新，无需更新"
    ));
}

log_message("数据库结构更新完成");
?> 