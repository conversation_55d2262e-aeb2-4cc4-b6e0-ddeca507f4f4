<?php
// 设置响应头
header('Content-Type: application/json');

// 加载数据库配置
include 'config.php';

// 准备响应数据
$response = array(
    'code' => 200,
    'links' => array()
);

// 判断是否为管理员请求
$isAdmin = isset($_GET['admin']) && $_GET['admin'] == '1';

// 如果是管理员请求，需要验证登录状态
if ($isAdmin) {
    session_start();
    
    // 验证管理员登录状态
    if (!isset($_SESSION['username'])) {
        $response['code'] = 403;
        $response['error'] = '未授权访问';
        echo json_encode($response);
        exit;
    }
}

try {
    // 检查links表是否存在
    $tableCheckSql = "SHOW TABLES LIKE 'links'";
    $tableResult = $conn->query($tableCheckSql);
    
    if ($tableResult && $tableResult->num_rows == 0) {
        // 表不存在，创建表
        $createTableSql = "CREATE TABLE IF NOT EXISTS `links` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(50) NOT NULL,
            `url` varchar(255) NOT NULL,
            `display_name` varchar(50) NOT NULL,
            `icon` varchar(50) NOT NULL DEFAULT 'fa-link',
            `color` varchar(20) NOT NULL DEFAULT '',
            `sort_order` int(11) NOT NULL DEFAULT 0,
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `name` (`name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        
        if (!$conn->query($createTableSql)) {
            throw new Exception("创建数据表失败: " . $conn->error);
        }
        
        // 插入默认链接数据
        $defaultLinks = array(
            array('buy_account', 'https://example.com/buy', '购买账号', 'fa-shopping-cart', '#44476a', 1, 1),
            array('faq', 'https://example.com/faq', '问题解答', 'fa-question-circle', '#2196F3', 2, 1),
            array('customer_service', 'https://example.com/service', '咨询客服', 'fa-headset', '#4CAF50', 3, 1),
            array('extra_link_1', '', '额外链接1', 'fa-link', '#FF9800', 4, 0),
            array('extra_link_2', '', '额外链接2', 'fa-link', '#9C27B0', 5, 0)
        );
        
        foreach ($defaultLinks as $link) {
            $insertSql = "INSERT INTO `links` (`name`, `url`, `display_name`, `icon`, `color`, `sort_order`, `is_active`) VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insertSql);
            $stmt->bind_param("sssssii", $link[0], $link[1], $link[2], $link[3], $link[4], $link[5], $link[6]);
            $stmt->execute();
        }
    } else {
        // 检查表结构是否需要更新
        $checkColumnSql = "SHOW COLUMNS FROM `links` LIKE 'display_name'";
        $columnResult = $conn->query($checkColumnSql);
        
        if ($columnResult && $columnResult->num_rows == 0) {
            // 需要更新表结构，添加新列
            $alterTableSql = "ALTER TABLE `links` 
                ADD COLUMN `display_name` varchar(50) NOT NULL DEFAULT '' AFTER `url`,
                ADD COLUMN `icon` varchar(50) NOT NULL DEFAULT 'fa-link' AFTER `display_name`,
                ADD COLUMN `color` varchar(20) NOT NULL DEFAULT '' AFTER `icon`,
                ADD COLUMN `sort_order` int(11) NOT NULL DEFAULT 0 AFTER `color`,
                ADD COLUMN `is_active` tinyint(1) NOT NULL DEFAULT 1 AFTER `sort_order`";
            
            if (!$conn->query($alterTableSql)) {
                throw new Exception("更新数据表结构失败: " . $conn->error);
            }
            
            // 为已有记录添加默认显示名称和图标
            $updateDefaults = array(
                array('buy_account', '购买账号', 'fa-shopping-cart', '#44476a', 1),
                array('faq', '问题解答', 'fa-question-circle', '#2196F3', 2),
                array('customer_service', '咨询客服', 'fa-headset', '#4CAF50', 3)
            );
            
            foreach ($updateDefaults as $update) {
                $updateSql = "UPDATE `links` SET `display_name` = ?, `icon` = ?, `color` = ?, `sort_order` = ? WHERE `name` = ?";
                $stmt = $conn->prepare($updateSql);
                $stmt->bind_param("ssssi", $update[1], $update[2], $update[3], $update[4], $update[0]);
                $stmt->execute();
            }
            
            // 添加额外链接
            $checkExtraSql = "SELECT COUNT(*) as count FROM `links` WHERE `name` = 'extra_link_1'";
            $extraResult = $conn->query($checkExtraSql);
            $extraRow = $extraResult->fetch_assoc();
            
            if ($extraRow['count'] == 0) {
                $extraLinks = array(
                    array('extra_link_1', '', '额外链接1', 'fa-link', '#FF9800', 4, 0),
                    array('extra_link_2', '', '额外链接2', 'fa-link', '#9C27B0', 5, 0)
                );
                
                foreach ($extraLinks as $link) {
                    $insertSql = "INSERT INTO `links` (`name`, `url`, `display_name`, `icon`, `color`, `sort_order`, `is_active`) VALUES (?, ?, ?, ?, ?, ?, ?)";
                    $stmt = $conn->prepare($insertSql);
                    $stmt->bind_param("sssssii", $link[0], $link[1], $link[2], $link[3], $link[4], $link[5], $link[6]);
                    $stmt->execute();
                }
            }
        }
        
        // 检查是否存在颜色字段
        $checkColorColumnSql = "SHOW COLUMNS FROM `links` LIKE 'color'";
        $colorColumnResult = $conn->query($checkColorColumnSql);
        
        if ($colorColumnResult && $colorColumnResult->num_rows == 0) {
            // 需要添加颜色字段
            $alterColorTableSql = "ALTER TABLE `links` ADD COLUMN `color` varchar(20) NOT NULL DEFAULT '' AFTER `icon`";
            
            if (!$conn->query($alterColorTableSql)) {
                throw new Exception("更新数据表添加颜色字段失败: " . $conn->error);
            }
            
            // 为已有链接添加默认颜色
            $defaultColors = array(
                array('buy_account', '#44476a'),
                array('faq', '#2196F3'),
                array('customer_service', '#4CAF50'),
                array('extra_link_1', '#FF9800'),
                array('extra_link_2', '#9C27B0')
            );
            
            foreach ($defaultColors as $colorUpdate) {
                $updateColorSql = "UPDATE `links` SET `color` = ? WHERE `name` = ?";
                $colorStmt = $conn->prepare($updateColorSql);
                $colorStmt->bind_param("ss", $colorUpdate[1], $colorUpdate[0]);
                $colorStmt->execute();
            }
        }
    }
    
    // 获取链接数据
    if ($isAdmin) {
        // 管理员获取所有链接
        $sql = "SELECT `name`, `url`, `display_name`, `icon`, `color`, `sort_order`, `is_active` FROM `links` ORDER BY `sort_order`";
    } else {
        // 前台只获取激活的链接
        $sql = "SELECT `name`, `url`, `display_name`, `icon`, `color`, `sort_order` FROM `links` WHERE `is_active` = 1 AND `url` != '' ORDER BY `sort_order`";
    }
    
    $result = $conn->query($sql);
    
    if ($result) {
        if ($isAdmin) {
            // 管理员获取完整数据
            $response['links'] = array();
            while ($row = $result->fetch_assoc()) {
                $response['links'][$row['name']] = array(
                    'url' => $row['url'],
                    'display_name' => $row['display_name'],
                    'icon' => $row['icon'],
                    'color' => $row['color'],
                    'sort_order' => $row['sort_order'],
                    'is_active' => $row['is_active']
                );
            }
        } else {
            // 前台获取简化数据
            $response['links'] = array();
            while ($row = $result->fetch_assoc()) {
                $response['links'][] = array(
                    'name' => $row['name'],
                    'url' => $row['url'],
                    'display_name' => $row['display_name'],
                    'icon' => $row['icon'],
                    'color' => $row['color']
                );
            }
        }
    } else {
        throw new Exception("查询链接数据失败: " . $conn->error);
    }
} catch (Exception $e) {
    $response['code'] = 500;
    $response['error'] = $e->getMessage();
}

echo json_encode($response);