<?php
// 安装锁定文件
$lock_file = 'install.lock';

// 检查是否已安装
if (file_exists($lock_file)) {
    die('<div style="font-family: \'Poppins\', sans-serif; background: #e0e5ec; min-height: 100vh; display: flex; justify-content: center; align-items: center; padding: 20px; text-align: center;">
        <div style="background: #e0e5ec; padding: 40px; border-radius: 20px; box-shadow: 8px 8px 15px rgba(163, 177, 198, 0.5), -8px -8px 15px rgba(255, 255, 255, 0.5); max-width: 600px;">
            <h1 style="color: #ff9800; margin-bottom: 20px;">⚠ 系统已安装</h1>
            <p style="margin-bottom: 25px; color: #555;">检测到 install.lock 文件，系统已经安装完成。</p>
            <p style="margin-bottom: 30px; color: #777;">如需重新安装，请先删除 install.lock 文件</p>
            <a href="../" style="display: inline-block; padding: 12px 30px; background: #4CAF50; color: white; text-decoration: none; border-radius: 50px; font-weight: 500; box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.5), -5px -5px 10px rgba(255, 255, 255, 0.5); transition: all 0.3s ease;">访问网站首页</a>
        </div>
    </div>');
}

// 环境检测
if (version_compare(PHP_VERSION, '7.0.0', '<')) {
    die('<div style="font-family: \'Poppins\', sans-serif; background: #e0e5ec; min-height: 100vh; display: flex; justify-content: center; align-items: center; padding: 20px; text-align: center;">
        <div style="background: #e0e5ec; padding: 40px; border-radius: 20px; box-shadow: 8px 8px 15px rgba(163, 177, 198, 0.5), -8px -8px 15px rgba(255, 255, 255, 0.5); max-width: 600px;">
            <h1 style="color: #f44336; margin-bottom: 20px;">❌ 环境不满足要求</h1>
            <p style="margin-bottom: 25px; color: #555;">需要PHP 7.0或更高版本</p>
            <p style="margin-bottom: 30px; color: #777;">当前版本: '.PHP_VERSION.'</p>
            <a href="https://www.php.net/downloads.php" target="_blank" style="display: inline-block; padding: 12px 30px; background: #2196F3; color: white; text-decoration: none; border-radius: 50px; font-weight: 500; box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.5), -5px -5px 10px rgba(255, 255, 255, 0.5); transition: all 0.3s ease;">下载新版PHP</a>
        </div>
    </div>');
}

// 定义日志文件
$log_file = 'install.log';

// 记录日志函数
function log_message($message) {
    global $log_file;
    file_put_contents($log_file, date('Y-m-d H:i:s') . " - " . $message . "\n", FILE_APPEND);
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 记录开始安装
    log_message("开始安装流程");
    
    // 获取表单数据
    $servername = trim($_POST['servername']);
    $username = trim($_POST['username']);
    $password = trim($_POST['password']);
    $dbname = trim($_POST['dbname']);
    $admin_username = trim($_POST['admin_username'] ?? 'admin');
    $admin_password = trim($_POST['admin_password'] ?? '123456');
    
    // 验证输入
    if (empty($servername) || empty($username) || empty($dbname)) {
        $error = '请填写所有必填字段';
        log_message("表单验证失败: " . $error);
    } else {
        // 测试数据库连接
        try {
            log_message("尝试连接数据库: $servername, $username, $dbname");
            $conn = new mysqli($servername, $username, $password);
            
            if ($conn->connect_error) {
                throw new Exception("连接失败: " . $conn->connect_error);
            }
            
            // 检查数据库是否存在
            $db_exists = $conn->select_db($dbname);
            
            if (!$db_exists) {
                log_message("数据库不存在，尝试创建: $dbname");
                // 尝试创建数据库
                if (!$conn->query("CREATE DATABASE IF NOT EXISTS `$dbname` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci")) {
                    throw new Exception("创建数据库失败: " . $conn->error);
                }
                log_message("成功创建数据库: $dbname");
                $conn->select_db($dbname);
            }
            
            // 创建配置文件
            $config_content = <<<EOT
<?php
// 数据库配置
\$servername = "$servername";
\$username = "$username";
\$password = "$password";
\$dbname = "$dbname";

// 创建连接
\$conn = new mysqli(\$servername, \$username, \$password, \$dbname);

// 检查连接
if (\$conn->connect_error) {
    die("连接失败: " . \$conn->connect_error);
}
EOT;
            
            // 写入配置文件
            log_message("尝试写入配置文件");
            if (!file_put_contents('config.php', $config_content)) {
                throw new Exception("无法写入配置文件 config.php，请检查目录权限");
            }
            log_message("成功写入配置文件");
            
            // 导入SQL文件
            $sql_file = 'db.sql';
            if (file_exists($sql_file)) {
                log_message("开始导入数据库结构: $sql_file");
                // 读取SQL文件内容
                $sql = file_get_contents($sql_file);
                
                // 如果修改了管理员信息，替换默认密码
                if ($admin_username != 'admin' || $admin_password != '123456') {
                    $admin_md5 = md5($admin_password);
                    log_message("自定义管理员信息: $admin_username, [密码已加密]");
                    $sql = preg_replace(
                        "/INSERT INTO `admin` \(`id`, `username`, `password`\) VALUES\s*\(\d+,\s*'admin',\s*'[^']+'\);/i",
                        "INSERT INTO `admin` (`id`, `username`, `password`) VALUES(1, '$admin_username', '$admin_md5');",
                        $sql
                    );
                }
                
                // 执行SQL语句
                if ($conn->multi_query($sql)) {
                    log_message("开始执行SQL查询");
                    $success = true;
                    $error_count = 0;
                    
                    do {
                        // 清空结果
                        if ($result = $conn->store_result()) {
                            $result->free();
                        }
                        
                        // 检查每个查询的执行结果
                        if ($conn->errno) {
                            log_message("SQL执行错误: " . $conn->error);
                            $error_count++;
                        }
                    } while ($conn->more_results() && $conn->next_result());
                    
                    if ($error_count > 0) {
                        log_message("SQL导入完成，但有 $error_count 个错误");
                    } else {
                        log_message("SQL导入完成，无错误");
                    }
                } else {
                    throw new Exception("导入数据库失败: " . $conn->error);
                }
            } else {
                throw new Exception("找不到SQL文件: $sql_file");
            }
            
            // 创建安装锁定文件
            log_message("创建安装锁定文件");
            file_put_contents($lock_file, '安装完成于 ' . date('Y-m-d H:i:s'));
            
            // 安装完成，跳转或显示成功信息
            log_message("安装完成，跳转到成功页面");
            header('Location: install-success.html');
            exit;
            
        } catch (Exception $e) {
            $error = $e->getMessage();
            log_message("安装失败: " . $error);
        }
    }
}
?>




<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密管理系统安装</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4CAF50;
            --error-color: #f44336;
            --bg-color: #e0e5ec;
            --text-color: #555;
            --shadow-light: rgba(255, 255, 255, 0.5);
            --shadow-dark: rgba(163, 177, 198, 0.5);
        }
        
        * {
            box-sizing: border-box;
            transition: all 0.3s ease;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            width: 100%;
            max-width: 700px;
            background: var(--bg-color);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 
                8px 8px 15px var(--shadow-dark),
                -8px -8px 15px var(--shadow-light);
            animation: fadeIn 0.8s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        h1 {
            color: var(--primary-color);
            text-align: center;
            margin-bottom: 30px;
            font-weight: 600;
            text-shadow: 
                2px 2px 4px var(--shadow-dark),
                -2px -2px 4px var(--shadow-light);
        }
        
        .error {
            color: var(--error-color);
            background: rgba(244, 67, 54, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 25px;
            text-align: center;
            box-shadow: 
                inset 3px 3px 6px var(--shadow-dark),
                inset -3px -3px 6px var(--shadow-light);
            animation: shake 0.5s ease;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            20%, 60% { transform: translateX(-5px); }
            40%, 80% { transform: translateX(5px); }
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #444;
        }
        
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 15px 20px;
            border: none;
            border-radius: 50px;
            background: var(--bg-color);
            box-shadow: 
                inset 5px 5px 10px var(--shadow-dark),
                inset -5px -5px 10px var(--shadow-light);
            font-family: 'Poppins', sans-serif;
            font-size: 16px;
            color: var(--text-color);
            outline: none;
        }
        
        input[type="text"]:focus,
        input[type="password"]:focus {
            box-shadow: 
                inset 3px 3px 6px var(--shadow-dark),
                inset -3px -3px 6px var(--shadow-light);
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
            width: 100%;
            box-shadow: 
                5px 5px 10px var(--shadow-dark),
                -5px -5px 10px var(--shadow-light);
            position: relative;
            overflow: hidden;
        }
        
        .btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(255,255,255,0.3), transparent);
            transform: translateY(-100%);
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 
                8px 8px 15px var(--shadow-dark),
                -8px -8px 15px var(--shadow-light);
        }
        
        .btn:hover::after {
            animation: shine 0.6s ease;
        }
        
        @keyframes shine {
            to { transform: translateY(100%); }
        }
        
        .btn:active {
            transform: translateY(1px);
            box-shadow: 
                2px 2px 5px var(--shadow-dark),
                -2px -2px 5px var(--shadow-light);
        }
        
        .requirements {
            background: var(--bg-color);
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            box-shadow: 
                inset 3px 3px 6px var(--shadow-dark),
                inset -3px -3px 6px var(--shadow-light);
        }
        
        .requirements h3 {
            margin-top: 0;
            color: #333;
            font-weight: 500;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        
        .requirement-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .requirement-item .icon {
            margin-right: 10px;
            font-size: 20px;
        }
        
        .requirement-item.valid .icon {
            color: var(--primary-color);
        }
        
        .requirement-item.invalid .icon {
            color: var(--error-color);
        }
        
        .divider {
            border-top: 1px solid rgba(0,0,0,0.1);
            margin: 30px 0;
        }
        
        .section-title {
            color: #333;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 25px;
            }
            
            input[type="text"],
            input[type="password"] {
                padding: 12px 15px;
            }
            
            .btn {
                padding: 12px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>卡密管理系统安装</h1>
        
        <?php if (isset($error)): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <form method="post" action="">
            <h2 class="section-title">数据库配置</h2>
            
            <div class="form-group">
                <label for="servername">数据库服务器</label>
                <input type="text" id="servername" name="servername" value="<?php echo isset($servername) ? htmlspecialchars($servername) : 'localhost'; ?>" required>
            </div>
            
            <div class="form-group">
                <label for="username">数据库用户名</label>
                <input type="text" id="username" name="username" value="<?php echo isset($username) ? htmlspecialchars($username) : 'root'; ?>" required>
            </div>
            
            <div class="form-group">
                <label for="password">数据库密码</label>
                <input type="password" id="password" name="password" value="<?php echo isset($password) ? htmlspecialchars($password) : ''; ?>">
            </div>
            
            <div class="form-group">
                <label for="dbname">数据库名</label>
                <input type="text" id="dbname" name="dbname" value="<?php echo isset($dbname) ? htmlspecialchars($dbname) : 'kami_system'; ?>" required>
            </div>
            
            <div class="divider"></div>
            
            <h2 class="section-title">管理员账号设置</h2>
            
            <div class="form-group">
                <label for="admin_username">管理员用户名</label>
                <input type="text" id="admin_username" name="admin_username" value="<?php echo isset($admin_username) ? htmlspecialchars($admin_username) : 'admin'; ?>" required>
            </div>
            
            <div class="form-group">
                <label for="admin_password">管理员密码</label>
                <input type="password" id="admin_password" name="admin_password" value="<?php echo isset($admin_password) ? htmlspecialchars($admin_password) : '123456'; ?>" required>
                <small style="display: block; margin-top: 5px; color: #777;">默认密码: 123456</small>
            </div>
            
            <div class="requirements">
                <h3>系统要求</h3>
                <div class="requirement-item valid">
                    <span class="icon">✓</span>
                    <span>PHP 7.0+ (当前: <?php echo PHP_VERSION; ?>)</span>
                </div>
                <div class="requirement-item <?php echo extension_loaded('mysqli') ? 'valid' : 'invalid'; ?>">
                    <span class="icon"><?php echo extension_loaded('mysqli') ? '✓' : '✗'; ?></span>
                    <span>MySQLi 扩展</span>
                </div>
                <div class="requirement-item <?php echo is_writable('.') ? 'valid' : 'invalid'; ?>">
                    <span class="icon"><?php echo is_writable('.') ? '✓' : '✗'; ?></span>
                    <span>目录可写权限</span>
                </div>
            </div>
            
            <button type="submit" class="btn">开始安装</button>
        </form>
    </div>
</body>
</html>