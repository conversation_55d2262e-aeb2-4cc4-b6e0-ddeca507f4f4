<?php
// 数据库配置
session_start();

if (!isset($_SESSION['username'])) {
    header("Location: ../login.php");
    exit();
}
include 'config.php';

// 设置错误处理
$json_data = array();

try {
    // 检查kami表是否存在
    $tableCheckSql = "SHOW TABLES LIKE 'kami'";
    $tableResult = $conn->query($tableCheckSql);
    
    if ($tableResult && $tableResult->num_rows == 0) {
        // 表不存在，创建表
        $createTableSql = "CREATE TABLE IF NOT EXISTS `kami` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `kami` varchar(255) NOT NULL,
            `type` varchar(50) NOT NULL,
            `zt` int(11) NOT NULL DEFAULT '0',
            `vip_time` varchar(255) DEFAULT '',
            `user` varchar(255) DEFAULT '',
            PRIMARY KEY (`id`),
            UNIQUE KEY `kami` (`kami`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        
        if (!$conn->query($createTableSql)) {
            throw new Exception("创建数据表失败: " . $conn->error);
        }
    }

    // 获取表的数量，根据特定条件
    function get_table_count_by_condition($conn, $table_name, $condition = '') {
        try {
            $sql = "SELECT COUNT(*) as count FROM $table_name" . ($condition ? " WHERE $condition" : "");
            $result = $conn->query($sql);

            if (!$result) {
                throw new Exception("查询失败: " . $conn->error);
            }

            if ($result->num_rows > 0) {
                $row = $result->fetch_assoc();
                return $row['count'];
            } else {
                return 0;
            }
        } catch (Exception $e) {
            return 0;
        }
    }

    // 获取kami表的总数
    $kami_count_total = get_table_count_by_condition($conn, 'kami');

    // 获取kami表中未使用的卡密数量（use_time为NULL）
    $kami_count_unused = get_table_count_by_condition($conn, 'kami', "use_time IS NULL");

    // 获取kami表中已使用的卡密数量（use_time不为NULL）
    $kami_count_used = get_table_count_by_condition($conn, 'kami', "use_time IS NOT NULL");

    // 将数量转换为JSON数组
    $json_data = array(
        'kami' => array(
            'total' => $kami_count_total,
            'zt0' => $kami_count_unused,
            'zt1' => $kami_count_used
        ),
        'code' => 200
    );
} catch (Exception $e) {
    $json_data = array(
        'code' => 500,
        'error' => $e->getMessage(),
        'kami' => array(
            'total' => 0,
            'zt0' => 0,
            'zt1' => 0
        )
    );
}

// 输出JSON数组
header('Content-Type: application/json');
echo json_encode($json_data);

// 关闭数据库连接
$conn->close();
?>