<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安装成功</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap');
        
        :root {
            --primary-color: #4CAF50;
            --secondary-color: #2196F3;
            --bg-color: #e0e5ec;
            --shadow-light: rgba(255, 255, 255, 0.5);
            --shadow-dark: rgba(163, 177, 198, 0.5);
        }
        
        * {
            box-sizing: border-box;
            transition: all 0.3s ease;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: var(--bg-color);
        }
        
        .container {
            max-width: 650px;
            width: 100%;
            background: var(--bg-color);
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 
                8px 8px 15px var(--shadow-dark),
                -8px -8px 15px var(--shadow-light);
            animation: fadeIn 0.8s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        h1 {
            color: var(--primary-color);
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .success-icon {
            font-size: 80px;
            color: var(--primary-color);
            margin: 20px 0;
            text-shadow: 
                3px 3px 6px var(--shadow-dark),
                -3px -3px 6px var(--shadow-light);
            animation: bounce 1s infinite alternate;
        }
        
        @keyframes bounce {
            from { transform: translateY(0); }
            to { transform: translateY(-10px); }
        }
        
        p {
            color: #555;
            margin-bottom: 20px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 50px;
            margin: 10px 8px;
            font-weight: 500;
            letter-spacing: 1px;
            text-transform: uppercase;
            font-size: 14px;
            border: none;
            cursor: pointer;
            box-shadow: 
                5px 5px 10px var(--shadow-dark),
                -5px -5px 10px var(--shadow-light);
            position: relative;
            overflow: hidden;
        }
        
        .btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(255,255,255,0.3), transparent);
            transform: translateY(-100%);
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 
                8px 8px 15px var(--shadow-dark),
                -8px -8px 15px var(--shadow-light);
        }
        
        .btn:hover::after {
            animation: shine 0.6s ease;
        }
        
        @keyframes shine {
            to { transform: translateY(100%); }
        }
        
        .btn:active {
            transform: translateY(1px);
            box-shadow: 
                2px 2px 5px var(--shadow-dark),
                -2px -2px 5px var(--shadow-light);
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-secondary {
            background: var(--secondary-color);
            color: white;
        }
        
        .credentials {
            background: var(--bg-color);
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            text-align: left;
            box-shadow: 
                inset 3px 3px 6px var(--shadow-dark),
                inset -3px -3px 6px var(--shadow-light);
            animation: slideIn 0.6s ease-out;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        .credentials h3 {
            margin-top: 0;
            color: #333;
            font-weight: 500;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        
        .credentials p {
            margin: 10px 0;
            display: flex;
            align-items: center;
        }
        
        .credentials strong {
            min-width: 80px;
            display: inline-block;
            color: #444;
        }
        
        .credentials a {
            color: var(--secondary-color);
            text-decoration: none;
            position: relative;
        }
        
        .credentials a::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 1px;
            background: var(--secondary-color);
            transition: width 0.3s ease;
        }
        
        .credentials a:hover::after {
            width: 100%;
        }
        
        .warning {
            color: #ff9800;
            font-weight: 500;
            margin-top: 15px;
            padding: 10px;
            background: rgba(255, 152, 0, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }
        
        .warning::before {
            content: '⚠';
            margin-right: 8px;
            font-size: 18px;
        }
        
        .footer-note {
            margin-top: 30px;
            font-size: 0.85em;
            color: #777;
        }
        
        /* 响应式设计 */
        @media (max-width: 600px) {
            .container {
                padding: 25px;
            }
            
            .btn {
                display: block;
                width: 100%;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✓</div>
        <h1>安装成功</h1>
        <p>卡密管理系统已成功安装</p>
        
        <div class="credentials">
            <h3>后台管理信息</h3>
            <p><strong>后台地址：</strong> <a href="/admin.html" target="_blank">/admin.html</a></p>
            <p><strong>用户名：</strong> admin</p>
            <p><strong>密码：</strong> 123456</p>
            <div class="warning">首次登录后请立即修改密码！</div>
        </div>
        
        <div>
            <a href="/admin.html" class="btn btn-primary">访问后台管理</a>
        </div>
        
        <p class="footer-note">
            请删除或重命名安装目录以确保系统安全
        </p>
    </div>
</body>
</html>