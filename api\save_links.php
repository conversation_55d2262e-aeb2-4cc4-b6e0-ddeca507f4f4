<?php
// 设置响应头
header('Content-Type: application/json');

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(array(
        'code' => 405,
        'error' => '方法不允许'
    ));
    exit;
}

// 启动会话
session_start();

// 验证管理员登录状态
if (!isset($_SESSION['username'])) {
    http_response_code(403);
    echo json_encode(array(
        'code' => 403,
        'error' => '未授权访问'
    ));
    exit;
}

// 加载数据库配置
include 'config.php';

// 准备响应数据
$response = array(
    'code' => 200,
    'msg' => '链接设置已成功保存'
);

try {
    // 检查links表是否存在
    $tableCheckSql = "SHOW TABLES LIKE 'links'";
    $tableResult = $conn->query($tableCheckSql);
    
    if ($tableResult && $tableResult->num_rows == 0) {
        // 表不存在，创建表
        $createTableSql = "CREATE TABLE IF NOT EXISTS `links` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(50) NOT NULL,
            `url` varchar(255) NOT NULL,
            `display_name` varchar(50) NOT NULL,
            `icon` varchar(50) NOT NULL DEFAULT 'fa-link',
            `color` varchar(20) NOT NULL DEFAULT '',
            `sort_order` int(11) NOT NULL DEFAULT 0,
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `name` (`name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        
        if (!$conn->query($createTableSql)) {
            throw new Exception("创建数据表失败: " . $conn->error);
        }
    } else {
        // 检查表结构是否需要更新
        $checkColumnSql = "SHOW COLUMNS FROM `links` LIKE 'display_name'";
        $columnResult = $conn->query($checkColumnSql);
        
        if ($columnResult && $columnResult->num_rows == 0) {
            // 需要更新表结构，添加新列
            $alterTableSql = "ALTER TABLE `links` 
                ADD COLUMN `display_name` varchar(50) NOT NULL DEFAULT '' AFTER `url`,
                ADD COLUMN `icon` varchar(50) NOT NULL DEFAULT 'fa-link' AFTER `display_name`,
                ADD COLUMN `sort_order` int(11) NOT NULL DEFAULT 0 AFTER `icon`,
                ADD COLUMN `is_active` tinyint(1) NOT NULL DEFAULT 1 AFTER `sort_order`";
            
            if (!$conn->query($alterTableSql)) {
                throw new Exception("更新数据表结构失败: " . $conn->error);
            }
        }
        
        // 检查是否存在颜色字段
        $checkColorColumnSql = "SHOW COLUMNS FROM `links` LIKE 'color'";
        $colorColumnResult = $conn->query($checkColorColumnSql);
        
        if ($colorColumnResult && $colorColumnResult->num_rows == 0) {
            // 需要添加颜色字段
            $alterColorTableSql = "ALTER TABLE `links` ADD COLUMN `color` varchar(20) NOT NULL DEFAULT '' AFTER `icon`";
            
            if (!$conn->query($alterColorTableSql)) {
                throw new Exception("更新数据表添加颜色字段失败: " . $conn->error);
            }
        }
    }
    
    // 开始事务
    $conn->begin_transaction();
    
    // 处理每个链接
    $linkNames = array('buy_account', 'faq', 'customer_service', 'extra_link_1', 'extra_link_2');
    
    foreach ($linkNames as $index => $name) {
        $url = isset($_POST[$name.'_url']) ? trim($_POST[$name.'_url']) : '';
        $displayName = isset($_POST[$name.'_display_name']) ? trim($_POST[$name.'_display_name']) : '';
        $icon = isset($_POST[$name.'_icon']) ? trim($_POST[$name.'_icon']) : 'fa-link';
        $color = isset($_POST[$name.'_color']) ? trim($_POST[$name.'_color']) : '';
        $sortOrder = $index + 1;
        $isActive = isset($_POST[$name.'_active']) ? 1 : 0;
        
        // 如果是老字段，保持兼容性
        if ($name === 'buy_account' && !isset($_POST[$name.'_url'])) {
            $url = isset($_POST['buy_account']) ? trim($_POST['buy_account']) : '';
            $displayName = '购买账号';
            $icon = 'fa-shopping-cart';
            $color = '#44476a'; // 默认颜色
            $isActive = !empty($url) ? 1 : 0;
        } else if ($name === 'faq' && !isset($_POST[$name.'_url'])) {
            $url = isset($_POST['faq']) ? trim($_POST['faq']) : '';
            $displayName = '问题解答';
            $icon = 'fa-question-circle';
            $color = '#2196F3'; // 默认颜色
            $isActive = !empty($url) ? 1 : 0;
        } else if ($name === 'customer_service' && !isset($_POST[$name.'_url'])) {
            $url = isset($_POST['customer_service']) ? trim($_POST['customer_service']) : '';
            $displayName = '咨询客服';
            $icon = 'fa-headset';
            $color = '#4CAF50'; // 默认颜色
            $isActive = !empty($url) ? 1 : 0;
        }
        
        // 如果没有提供显示名称，设置默认值
        if (empty($displayName)) {
            switch($name) {
                case 'buy_account': $displayName = '购买账号'; break;
                case 'faq': $displayName = '问题解答'; break;
                case 'customer_service': $displayName = '咨询客服'; break;
                case 'extra_link_1': $displayName = '额外链接1'; break;
                case 'extra_link_2': $displayName = '额外链接2'; break;
            }
        }
        
        // 检查记录是否存在
        $checkSql = "SELECT COUNT(*) as count FROM `links` WHERE `name` = ?";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bind_param("s", $name);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();
        $row = $checkResult->fetch_assoc();
        
        if ($row['count'] > 0) {
            // 更新记录
            $updateSql = "UPDATE `links` SET `url` = ?, `display_name` = ?, `icon` = ?, `color` = ?, `sort_order` = ?, `is_active` = ? WHERE `name` = ?";
            $updateStmt = $conn->prepare($updateSql);
            $updateStmt->bind_param("ssssiis", $url, $displayName, $icon, $color, $sortOrder, $isActive, $name);
            
            if (!$updateStmt->execute()) {
                throw new Exception("更新链接失败: " . $conn->error);
            }
        } else {
            // 插入记录
            $insertSql = "INSERT INTO `links` (`name`, `url`, `display_name`, `icon`, `color`, `sort_order`, `is_active`) VALUES (?, ?, ?, ?, ?, ?, ?)";
            $insertStmt = $conn->prepare($insertSql);
            $insertStmt->bind_param("sssssii", $name, $url, $displayName, $icon, $color, $sortOrder, $isActive);
            
            if (!$insertStmt->execute()) {
                throw new Exception("插入链接失败: " . $conn->error);
            }
        }
    }
    
    // 提交事务
    $conn->commit();
} catch (Exception $e) {
    // 回滚事务
    if ($conn->connect_error) {
        $conn->rollback();
    }
    
    $response['code'] = 500;
    $response['error'] = $e->getMessage();
}

// 输出JSON响应
echo json_encode($response);

// 关闭数据库连接
$conn->close();
?> 