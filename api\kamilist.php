<?php
// 引入配置文件
require 'config.php';
session_start();

if (!isset($_SESSION['username'])) {
    header("Location: ../login.html");
    exit();
}

// 创建PDO实例
try {
    $pdo = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8", $username, $password);
    // 设置错误模式为异常
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 获取分页参数
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
    $offset = ($page - 1) * $limit;
    
    // 获取搜索和筛选参数
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $type = isset($_GET['type']) ? trim($_GET['type']) : '';
    $status = isset($_GET['status']) ? trim($_GET['status']) : '';
    
    // 构建WHERE条件
    $whereConditions = [];
    $params = [];
    
    // 搜索条件
    if (!empty($search)) {
        $whereConditions[] = "(kami LIKE :search OR remark LIKE :search OR new_account LIKE :search)";
        $params[':search'] = "%{$search}%";
    }
    
    // 类型筛选
    if (!empty($type)) {
        $whereConditions[] = "type = :type";
        $params[':type'] = $type;
    }
    
    // 状态筛选
    if ($status === '0') {
        $whereConditions[] = "use_time IS NULL";
    } elseif ($status === '1') {
        $whereConditions[] = "use_time IS NOT NULL";
    }
    
    // 组合WHERE子句
    $whereClause = !empty($whereConditions) ? "WHERE " . implode(' AND ', $whereConditions) : "";
    
    // 计算总记录数
    $countSql = "SELECT COUNT(*) as total FROM kami $whereClause";
    $countStmt = $pdo->prepare($countSql);
    foreach ($params as $key => $value) {
        $countStmt->bindValue($key, $value);
    }
    $countStmt->execute();
    $countRow = $countStmt->fetch(PDO::FETCH_ASSOC);
    $count = $countRow['total'];
    
    // 准备SQL查询，包含分页和筛选
    $sql = "SELECT id, kami, type, valid_days, device_limit, password, remark, new_account, new_password,
            DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time, 
            DATE_FORMAT(use_time, '%Y-%m-%d %H:%i:%s') as use_time,
            DATE_FORMAT(expiry_time, '%Y-%m-%d %H:%i:%s') as expiry_time
            FROM kami 
            $whereClause
            ORDER BY id DESC 
            LIMIT :offset, :limit";
    
    $stmt = $pdo->prepare($sql);
    // 绑定搜索和筛选参数
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    
    // 获取查询结果
    $cardList = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理每条记录，添加状态字段
    foreach ($cardList as &$card) {
        // 根据use_time判断状态
        $card['status'] = $card['use_time'] ? "已使用" : "未使用";
        
        // 如果已使用但没有到期时间，根据use_time和valid_days计算
        if ($card['use_time'] && !$card['expiry_time'] && $card['valid_days'] > 0) {
            $useTime = strtotime($card['use_time']);
            $expiryTime = date('Y-m-d H:i:s', strtotime("+{$card['valid_days']} days", $useTime));
            $card['expiry_time'] = $expiryTime;
        }
    }
    
    // 构建JSON格式的输出
    $output = [
        "code" => 200,
        "msg" => "",
        "count" => $count,
        "data" => $cardList
    ];
    
    // 设置响应头为JSON
    header('Content-Type: application/json');
    // 输出JSON数据
    echo json_encode($output, JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    // 错误处理
    $output = [
        "code" => 500,
        "msg" => $e->getMessage(),
        "count" => 0,
        "data" => []
    ];
    header('Content-Type: application/json');
    echo json_encode($output, JSON_UNESCAPED_UNICODE);
}
?>