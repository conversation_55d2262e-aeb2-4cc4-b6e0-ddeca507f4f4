<?php

// 设置缓存控制头，确保每次都获取最新数据
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

// 数据库配置
include 'config.php';

// 检查是否是 GET 请求
if ($_SERVER["REQUEST_METHOD"] == "GET") {
    // 获取 GET 数据
    $kami = $_GET['kami'] ?? '';

    // 检查卡密是否存在
    $checkStmt = $conn->prepare("SELECT * FROM kami WHERE kami = ?");
    $checkStmt->bind_param("s", $kami);
    $checkStmt->execute();
    $result = $checkStmt->get_result();
    $row = $result->fetch_assoc();
    if ($row) {
        // 如果卡密存在
        // 检查卡密状态 - 根据use_time判断
        $status = $row['use_time'] ? "已使用" : "未使用";
        
        // 如果是首次查询（未使用状态），则更新使用时间和到期时间
        if ($status === "未使用") {
            $now = date('Y-m-d H:i:s');
            $valid_days = $row['valid_days'];
            
            // 计算到期时间（当前时间 + 有效天数）
            $expiry_time = date('Y-m-d H:i:s', strtotime("+{$valid_days} days", strtotime($now)));
            
            // 更新数据库中的使用时间和到期时间
            $updateStmt = $conn->prepare("UPDATE kami SET use_time = ?, expiry_time = ? WHERE kami = ?");
            $updateStmt->bind_param("sss", $now, $expiry_time, $kami);
            $updateStmt->execute();
            $updateStmt->close();
            
            // 更新状态为已使用
            $status = "已使用";
            
            // 更新当前行数据，以便后续处理
            $row['use_time'] = $now;
            $row['expiry_time'] = $expiry_time;
        }
        
        // 设备数量显示
        $deviceLimit = $row['device_limit'] == 1 ? "仅限1台电脑" : "独享2台电脑";
        
        // 构建响应
        $response = [
            "code" => "200",
            "msg" => "查询成功",
            "type" => $row['type'],
            "valid_days" => $row['valid_days'],
            "device_limit" => $deviceLimit,
            "status" => $status
        ];
        
        // 添加到期时间到响应
        if (!empty($row['expiry_time'])) {
            // 返回完整的日期时间信息，包括时分秒
            $response["expiry_time"] = $row['expiry_time'];
        }
        
        // 如果有密码，添加到响应中
        if (!empty($row['password'])) {
            $response["password"] = $row['password'];
        } else {
            $response["password"] = "无";
        }
        
        // 添加新账号信息到响应中（如果存在）
        if (isset($row['new_account']) && !empty($row['new_account'])) {
            $response["new_account"] = $row['new_account'];
            
            // 如果有新账号密码，添加到响应中
            if (isset($row['new_password']) && !empty($row['new_password'])) {
                $response["new_password"] = $row['new_password'];
            } else {
                // 如果新密码未设置，使用原密码
                $response["new_password"] = $response["password"];
            }
            
            // 设置账号已更换标志
            $response["account_changed"] = true;
        } else {
            $response["account_changed"] = false;
        }
        
        // 如果有备注，添加到响应中
        if (!empty($row['remark'])) {
            $response["remark"] = $row['remark'];
        }
    } else {
        // 如果卡密不存在
        $response = [
            "code" => "404",
            "msg" => "卡密不存在"
        ];
    }

    // 返回 JSON 响应
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
} else {
    // 如果不是 GET 请求，返回错误
    header("HTTP/1.1 405 Method Not Allowed");
    exit("仅支持 GET 请求");
}

// 关闭数据库连接
$conn->close();
?>