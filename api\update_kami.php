<?php
// 关闭直接显示错误，但记录它们
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'error.log');

// 设置内容类型为JSON
header('Content-Type: application/json');

// 会话配置
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // 如果使用HTTPS，设置为1

// 启动会话
session_start();

// 检查登录状态
if (!isset($_SESSION['username'])) {
    echo json_encode(array("code" => "401", "error" => "未授权访问"));
    exit();
}

// 数据库配置
include 'config.php';

// 检查数据库连接
if ($conn->connect_error) {
    echo json_encode(array("code" => "500", "error" => "数据库连接失败"));
    exit();
}

// 接收POST请求参数
$kami = isset($_POST['kami']) ? $conn->real_escape_string($_POST['kami']) : null;
$type = isset($_POST['type']) ? $conn->real_escape_string($_POST['type']) : null;
$valid_days = isset($_POST['valid_days']) ? intval($_POST['valid_days']) : 0;
$device_limit = isset($_POST['device_limit']) ? intval($_POST['device_limit']) : 1;
$password = isset($_POST['password']) ? $conn->real_escape_string($_POST['password']) : '';
$remark = isset($_POST['remark']) ? $conn->real_escape_string($_POST['remark']) : '';
$expiry_time = isset($_POST['expiry_time']) && !empty($_POST['expiry_time']) ? $conn->real_escape_string($_POST['expiry_time']) : null;
// 添加新账号和新密码字段
$new_account = isset($_POST['new_account']) ? $conn->real_escape_string($_POST['new_account']) : '';
$new_password = isset($_POST['new_password']) ? $conn->real_escape_string($_POST['new_password']) : '';

// 验证必要参数
if (!$kami) {
    echo json_encode(array("code" => "403", "error" => "卡密参数不能为空"));
    exit();
}

// 如果是直接编辑表格单元格，可能只提供了部分参数
// 检查是否为单元格编辑模式（只更新特定字段）
$is_cell_edit = isset($_POST['field']) && in_array($_POST['field'], ['new_account', 'new_password', 'expiry_time']);

// 如果不是单元格编辑模式，则需要验证完整参数
if (!$is_cell_edit && (!$type || $valid_days <= 0)) {
    echo json_encode(array("code" => "403", "error" => "参数不全或无效"));
    exit();
}

// 如果是单元格编辑模式，需要从数据库获取当前值
if ($is_cell_edit) {
    $field = $_POST['field'];
    $query = "SELECT type, valid_days, device_limit, password, remark, new_account, new_password FROM kami WHERE kami = ?";
    $stmt = $conn->prepare($query);
    if (!$stmt) {
        echo json_encode(array("code" => "500", "error" => "预处理语句失败"));
        exit();
    }
    
    $stmt->bind_param("s", $kami);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        
        // 使用数据库中的现有值
        if (!$type) $type = $row['type'];
        if ($valid_days <= 0) $valid_days = $row['valid_days'];
        if (!$device_limit) $device_limit = $row['device_limit'];
        if ($password === '') $password = $row['password'];
        if ($remark === '') $remark = $row['remark'];
        
        // 根据编辑的字段设置相应的值
        if ($field !== 'new_account' && $new_account === '') $new_account = $row['new_account'];
        if ($field !== 'new_password' && $new_password === '') $new_password = $row['new_password'];
    }
    
    $stmt->close();
}

try {
    // 记录更新操作到日志
    file_put_contents('update_kami.log', date('Y-m-d H:i:s') . " - 尝试更新卡密: $kami, 类型: $type, 有效天数: $valid_days, 设备限制: $device_limit, 密码: $password, 备注: $remark, 到期时间: $expiry_time, 新账号: $new_account, 新密码: $new_password\n", FILE_APPEND);
    
    // 处理到期时间
    $updateFields = "type = ?, valid_days = ?, device_limit = ?, password = ?, remark = ?, new_account = ?, new_password = ?";
    $bindTypes = "siissss";
    $bindParams = array($type, $valid_days, $device_limit, $password, $remark, $new_account, $new_password);
    
    // 如果提供了到期时间，添加到更新字段中
    if ($expiry_time) {
        // 确保到期时间格式正确，避免重复时间值
        $formatted_expiry_time = '';
        
        // 检查是否包含日期和时间部分
        if (preg_match('/^(\d{4}-\d{2}-\d{2})(?:\s+(\d{2}:\d{2}:\d{2}))?(?:\s+\d{2}:\d{2}:\d{2})?$/', $expiry_time, $matches)) {
            $date_part = $matches[1];
            // 如果提供了时间部分，使用它；否则使用默认值
            $time_part = isset($matches[2]) ? $matches[2] : '23:59:59';
            $formatted_expiry_time = $date_part . ' ' . $time_part;
        } else {
            // 如果格式不正确，尝试其他格式或使用默认格式
            $date_parts = explode(' ', $expiry_time);
            if (count($date_parts) >= 1) {
                $date = $date_parts[0];
                // 如果有时间部分，只取第一个时间部分
                $time = isset($date_parts[1]) ? $date_parts[1] : '23:59:59';
                $formatted_expiry_time = $date . ' ' . $time;
            } else {
                file_put_contents('update_kami.log', date('Y-m-d H:i:s') . " - 无效的日期格式: $expiry_time\n", FILE_APPEND);
                throw new Exception("无效的日期格式");
            }
        }
        
        file_put_contents('update_kami.log', date('Y-m-d H:i:s') . " - 格式化后的到期时间: $formatted_expiry_time\n", FILE_APPEND);
        
        $updateFields .= ", expiry_time = ?";
        $bindTypes .= "s";
        $bindParams[] = $formatted_expiry_time;
    }
    
    // 添加卡密到绑定参数
    $bindTypes .= "s";
    $bindParams[] = $kami;
    
    // 更新卡密信息
    $updateSql = "UPDATE kami SET $updateFields WHERE kami = ?";
    $stmt = $conn->prepare($updateSql);
    
    if (!$stmt) {
        file_put_contents('update_kami.log', date('Y-m-d H:i:s') . " - 预处理语句失败: " . $conn->error . "\n", FILE_APPEND);
        throw new Exception("预处理语句失败: " . $conn->error);
    }
    
    // 动态绑定参数
    $stmt->bind_param($bindTypes, ...$bindParams);
    
    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            file_put_contents('update_kami.log', date('Y-m-d H:i:s') . " - 卡密更新成功\n", FILE_APPEND);
            echo json_encode(array("code" => "200", "msg" => "卡密更新成功"));
        } else {
            file_put_contents('update_kami.log', date('Y-m-d H:i:s') . " - 未找到该卡密或信息未变更\n", FILE_APPEND);
            echo json_encode(array("code" => "404", "error" => "未找到该卡密或信息未变更"));
        }
    } else {
        file_put_contents('update_kami.log', date('Y-m-d H:i:s') . " - 执行更新失败: " . $stmt->error . "\n", FILE_APPEND);
        throw new Exception("执行更新失败: " . $stmt->error);
    }
    
    $stmt->close();
} catch (Exception $e) {
    echo json_encode(array("code" => "500", "error" => $e->getMessage()));
} finally {
    if (isset($conn) && $conn) {
        $conn->close();
    }
}
?> 