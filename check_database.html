<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库检查</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.3.0/dist/sweetalert2.min.css">
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: #ECF1F7;
            color: #44476a;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        /* 卡片样式 */
        .card {
            background-color: #ECF1F7;
            border-radius: 15px;
            box-shadow: 8px 8px 15px rgba(163, 177, 198, 0.5),
                        -8px -8px 15px rgba(255, 255, 255, 0.6);
            padding: 30px;
            max-width: 800px;
            width: 100%;
            margin: 20px;
            text-align: center;
        }

        h1 {
            color: #44476a;
            margin-bottom: 20px;
        }

        p {
            margin-bottom: 25px;
            color: #666;
        }

        /* 按钮样式 */
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 50px;
            background-color: #4CAF50;
            color: white;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.5),
                        -5px -5px 10px rgba(255, 255, 255, 0.6);
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 7px 7px 15px rgba(163, 177, 198, 0.5),
                        -7px -7px 15px rgba(255, 255, 255, 0.6);
        }

        .btn:active {
            transform: translateY(0);
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.5),
                        -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        .btn i {
            margin-right: 8px;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            background-color: #ECF1F7;
            box-shadow: inset 5px 5px 10px rgba(163, 177, 198, 0.5),
                        inset -5px -5px 10px rgba(255, 255, 255, 0.6);
            text-align: left;
            max-height: 500px;
            overflow-y: auto;
        }

        .result pre {
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .success {
            color: #4CAF50;
        }

        .error {
            color: #F44336;
        }

        .info-section {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 10px;
            background-color: #ECF1F7;
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.5),
                        inset -3px -3px 6px rgba(255, 255, 255, 0.6);
        }

        .info-section h3 {
            margin-bottom: 10px;
            color: #44476a;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        table th, table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }

        table th {
            background-color: rgba(0,0,0,0.05);
        }
    </style>
</head>
<body>
    <div class="card">
        <h1>数据库检查</h1>
        <p>此工具将检查数据库连接和卡密表的结构，以帮助诊断卡密生成问题。</p>
        <button id="check-btn" class="btn">
            <i class="fas fa-database"></i>
            检查数据库
        </button>
        <div id="result" class="result" style="display: none;">
            <div id="loading">正在检查数据库，请稍候...</div>
            <div id="db-info" style="display: none;">
                <h3>数据库信息</h3>
                <table id="db-info-table"></table>
            </div>
            <div id="table-structure" style="display: none;">
                <h3>卡密表结构</h3>
                <pre id="table-create-sql"></pre>
            </div>
            <div id="columns-info" style="display: none;">
                <h3>字段信息</h3>
                <table id="columns-table"></table>
            </div>
            <div id="test-result" style="display: none;">
                <h3>测试插入结果</h3>
                <pre id="test-insert-result"></pre>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.3.0/dist/sweetalert2.min.js"></script>
    <script>
        document.getElementById('check-btn').addEventListener('click', function() {
            const resultElem = document.getElementById('result');
            resultElem.style.display = 'block';
            document.getElementById('loading').style.display = 'block';
            document.getElementById('db-info').style.display = 'none';
            document.getElementById('table-structure').style.display = 'none';
            document.getElementById('columns-info').style.display = 'none';
            document.getElementById('test-result').style.display = 'none';
            
            // 发送请求
            fetch('api/check_db.php')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loading').style.display = 'none';
                    
                    if (data.code === "200") {
                        // 显示数据库信息
                        const dbInfoTable = document.getElementById('db-info-table');
                        dbInfoTable.innerHTML = `
                            <tr>
                                <th>数据库版本</th>
                                <td>${data.db_info.version}</td>
                            </tr>
                            <tr>
                                <th>字符集</th>
                                <td>${data.db_info.charset}</td>
                            </tr>
                            <tr>
                                <th>排序规则</th>
                                <td>${data.db_info.collation}</td>
                            </tr>
                        `;
                        document.getElementById('db-info').style.display = 'block';
                        
                        // 显示表结构
                        document.getElementById('table-create-sql').textContent = data.table_create_sql;
                        document.getElementById('table-structure').style.display = 'block';
                        
                        // 显示字段信息
                        const columnsTable = document.getElementById('columns-table');
                        columnsTable.innerHTML = `
                            <tr>
                                <th>字段名</th>
                                <th>类型</th>
                                <th>可为空</th>
                                <th>键</th>
                                <th>默认值</th>
                                <th>额外</th>
                            </tr>
                        `;
                        data.columns.forEach(column => {
                            columnsTable.innerHTML += `
                                <tr>
                                    <td>${column.Field}</td>
                                    <td>${column.Type}</td>
                                    <td>${column.Null}</td>
                                    <td>${column.Key}</td>
                                    <td>${column.Default || ''}</td>
                                    <td>${column.Extra || ''}</td>
                                </tr>
                            `;
                        });
                        document.getElementById('columns-info').style.display = 'block';
                        
                        // 显示测试插入结果
                        const testInsertResult = document.getElementById('test-insert-result');
                        if (data.test_insert.success) {
                            testInsertResult.innerHTML = `<span class="success">测试成功！</span>\n成功插入包含特殊字符的卡密：${data.test_insert.test_kami}`;
                        } else {
                            testInsertResult.innerHTML = `<span class="error">测试失败！</span>\n错误信息：${data.test_insert.error}\n测试卡密：${data.test_insert.test_kami}`;
                        }
                        document.getElementById('test-result').style.display = 'block';
                        
                        // 显示成功消息
                        Swal.fire({
                            title: '检查完成',
                            text: data.msg,
                            icon: 'success'
                        });
                    } else {
                        // 显示错误信息
                        Swal.fire({
                            title: '检查失败',
                            text: data.error || '操作失败，请稍后再试',
                            icon: 'error'
                        });
                    }
                })
                .catch(error => {
                    document.getElementById('loading').style.display = 'none';
                    console.error('检查失败:', error);
                    Swal.fire({
                        title: '检查失败',
                        text: '请求出错，请稍后再试',
                        icon: 'error'
                    });
                });
        });
    </script>
</body>
</html> 